<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Izy Mercado - Seu mercado fácil e inteligente. Compare preços, crie listas e faça pedidos de forma simples e rápida.">
  <meta name="keywords" content="mercado, supermercado, compras, lista de compras, preços, delivery">
  <meta name="author" content="Izy Mercado">
  <meta name="robots" content="index, follow">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://izymercado.com/">
  <meta property="og:title" content="Izy Mercado - Seu mercado fácil e inteligente">
  <meta property="og:description" content="Compare preços, crie listas e faça pedidos de forma simples e rápida.">
  <meta property="og:image" content="./LP Izy/logo-izy1.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://izymercado.com/">
  <meta property="twitter:title" content="Izy Mercado - Seu mercado fácil e inteligente">
  <meta property="twitter:description" content="Compare preços, crie listas e faça pedidos de forma simples e rápida.">
  <meta property="twitter:image" content="./LP Izy/logo-izy1.png">

  <title>Izy Mercado - Seu mercado fácil e inteligente</title>

  <!-- DNS prefetch for external resources -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com">
  <link rel="dns-prefetch" href="//script.google.com">

  <!-- Preconnect to critical third-party origins -->
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Preload critical resources -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="styles.css" as="style">
  <link rel="preload" href="./LP Izy/logo-izy1.png" as="image">
  <link rel="preload" href="./content-categorias.svg" as="image">

  <!-- Critical CSS (inline for performance) -->
  <style>
    /* Critical above-the-fold styles */
    *,*::before,*::after{box-sizing:border-box}*{margin:0;padding:0}html{font-size:16px;scroll-behavior:smooth}body{font-family:'Roboto',-apple-system,BlinkMacSystemFont,'Segoe UI',sans-serif;font-size:1rem;font-weight:400;line-height:1.5;color:#514D4F;background-color:#FFFFFF;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.header{position:fixed;top:0;left:0;right:0;background:rgba(255,255,255,0.95);backdrop-filter:blur(10px);border-bottom:1px solid #E6E6E6;z-index:1030;transition:all 250ms ease-in-out}.header__nav{display:flex;align-items:center;justify-content:space-between;padding:1rem 2rem;max-width:1200px;margin:0 auto}.hero{padding:calc(80px + 5rem) 1rem 5rem;background:linear-gradient(135deg,#fafafa 0%,#f5f5f4 100%);text-align:center;position:relative;overflow:hidden}.hero__title{font-size:clamp(1.875rem,5vw,3rem);margin-bottom:1.5rem;line-height:1.25}.btn{display:inline-flex;align-items:center;justify-content:center;gap:0.5rem;font-family:inherit;font-weight:500;text-align:center;text-decoration:none;border:2px solid transparent;border-radius:9999px;cursor:pointer;transition:all 250ms ease-in-out;position:relative;overflow:hidden;padding:1rem 2rem;font-size:1rem}.btn--primary{background:linear-gradient(90deg,#B771E5 0%,#ED9292 100%);color:#FFFFFF;border-color:transparent;box-shadow:0 4px 6px -1px rgba(0,0,0,0.1),0 2px 4px -1px rgba(0,0,0,0.06)}
  </style>

  <!-- Stylesheets -->
  <link rel="stylesheet" href="styles.css">
  <noscript><link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet"></noscript>

  <!-- Favicon and app icons -->
  <link rel="icon" type="image/png" sizes="32x32" href="./LP Izy/logo.png">
  <link rel="apple-touch-icon" href="./LP Izy/logo-izy1.png">

  <!-- Web App Manifest -->
  <link rel="manifest" href="/manifest.json">

  <!-- Theme color for mobile browsers -->
  <meta name="theme-color" content="#B771E5">
  <meta name="msapplication-TileColor" content="#B771E5">
</head>
<body>
  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="skip-link">Pular para o conteúdo principal</a>

  <header class="header" role="banner">
    <nav class="header__nav" role="navigation" aria-label="Navegação principal">
      <div class="header__logo">
        <picture class="header__logo-picture">
          <source srcset="./LP Izy/logo-izy1.png" type="image/png">
          <img
            src="./LP Izy/logo-izy1.png"
            alt="Izy Mercado"
            class="header__logo-img"
            width="120"
            height="40"
            loading="eager"
            decoding="sync"
          >
        </picture>
        <span class="header__logo-text">izy</span>
      </div>
      <button class="btn btn--primary header__cta" aria-label="Baixar aplicativo Izy Mercado">
        Baixar app
      </button>
    </nav>
  </header>

  <main id="main-content" role="main">
    <!-- Hero Section -->
    <section class="hero" aria-labelledby="hero-title">
      <div class="hero__container">
        <header class="hero__header">
          <h1 id="hero-title" class="hero__title">
            <span class="hero__title-light">Seu mercado,</span>
            <span class="hero__title-bold"> fácil e inteligente</span>
          </h1>
          <p class="hero__subtitle">
            Compare preços, crie listas personalizadas e faça seus pedidos de forma simples e rápida.
          </p>
        </header>

        <div class="hero__categories" role="img" aria-label="Categorias de produtos disponíveis">
          <img
            src="./content-categorias.svg"
            alt="Ícones das categorias: bebidas, casa, carnes e higiene"
            class="hero__categories-img"
            loading="eager"
            decoding="async"
            width="400"
            height="120"
          >
        </div>

        <div class="hero__cta">
          <button class="btn btn--primary btn--large hero__download-btn" aria-label="Baixar aplicativo Izy Mercado">
            <span class="btn__text">Baixar app</span>
            <span class="btn__icon" aria-hidden="true">📱</span>
          </button>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features" aria-labelledby="features-title">
      <div class="features__container">
        <header class="features__header">
          <h2 id="features-title" class="features__title">Como funciona</h2>
          <p class="features__subtitle">Descubra como é fácil fazer suas compras com o Izy</p>
        </header>

        <div class="features__grid">
          <article class="feature-card">
            <div class="feature-card__image-wrapper">
              <picture class="feature-card__picture">
                <source srcset="./LP Izy/img1.png" type="image/png">
                <img
                  src="./LP Izy/img1.png"
                  alt="Interface do aplicativo mostrando lista de compras"
                  class="feature-card__image"
                  loading="lazy"
                  decoding="async"
                  width="300"
                  height="200"
                >
              </picture>
            </div>
            <div class="feature-card__content">
              <h3 class="feature-card__title">Crie suas listas</h3>
              <p class="feature-card__description">Organize suas compras de forma inteligente e nunca mais esqueça nada.</p>
            </div>
          </article>

          <article class="feature-card">
            <div class="feature-card__image-wrapper">
              <picture class="feature-card__picture">
                <source srcset="./LP Izy/img2.png" type="image/png">
                <img
                  src="./LP Izy/img2.png"
                  alt="Comparação de preços entre diferentes estabelecimentos"
                  class="feature-card__image"
                  loading="lazy"
                  decoding="async"
                  width="300"
                  height="200"
                >
              </picture>
            </div>
            <div class="feature-card__content">
              <h3 class="feature-card__title">Compare preços</h3>
              <p class="feature-card__description">Encontre os melhores preços e economize em cada compra.</p>
            </div>
          </article>

          <article class="feature-card">
            <div class="feature-card__image-wrapper">
              <picture class="feature-card__picture">
                <source srcset="./LP Izy/img3.png" type="image/png">
                <img
                  src="./LP Izy/img3.png"
                  alt="Processo de finalização de pedido no aplicativo"
                  class="feature-card__image"
                  loading="lazy"
                  decoding="async"
                  width="300"
                  height="200"
                >
              </picture>
            </div>
            <div class="feature-card__content">
              <h3 class="feature-card__title">Faça seu pedido</h3>
              <p class="feature-card__description">Finalize suas compras com segurança e receba em casa.</p>
            </div>
          </article>
        </div>
      </div>
    </section>

    <!-- Steps Section -->
    <section class="steps" aria-labelledby="steps-title">
      <div class="steps__container">
        <header class="steps__header">
          <h2 id="steps-title" class="steps__title">Simples em 3 passos</h2>
          <p class="steps__subtitle">Veja como é fácil começar a economizar</p>
        </header>

        <div class="steps__grid">
          <div class="step">
            <div class="step__icon" aria-hidden="true">
              <span class="step__number">1</span>
            </div>
            <h3 class="step__title">Crie sua lista</h3>
            <p class="step__description">Monte sua lista de compras de forma rápida e organizada</p>
          </div>

          <div class="step">
            <div class="step__icon" aria-hidden="true">
              <span class="step__number">2</span>
            </div>
            <h3 class="step__title">Compare preços</h3>
            <p class="step__description">Encontre os melhores preços em diferentes estabelecimentos</p>
          </div>

          <div class="step">
            <div class="step__icon" aria-hidden="true">
              <span class="step__number">3</span>
            </div>
            <h3 class="step__title">Faça seu pedido</h3>
            <p class="step__description">Finalize sua compra e receba em casa com segurança</p>
          </div>
        </div>

        <div class="steps__visual" role="img" aria-label="Indicadores visuais dos passos do processo">
          <img
            src="./steps.svg"
            alt="Ilustração dos 3 passos: criar lista, comparar preços e fazer pedido"
            class="steps__visual-img"
            loading="lazy"
            decoding="async"
            width="600"
            height="120"
          >
        </div>

        <div class="steps__savings">
          <img
            src="./title.svg"
            alt="Economize com o Izy Mercado"
            class="steps__savings-img"
            loading="lazy"
            decoding="async"
            width="400"
            height="60"
          >
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section" aria-labelledby="cta-title">
      <div class="cta-section__container">
        <h2 id="cta-title" class="cta-section__title">Pronto para economizar?</h2>
        <p class="cta-section__subtitle">Baixe o app agora e comece a fazer compras mais inteligentes</p>

        <div class="cta-section__buttons">
          <button class="btn btn--secondary" aria-label="Entrar em contato com o Izy Mercado">
            Entre em contato
          </button>
          <button class="btn btn--primary" aria-label="Baixar aplicativo Izy Mercado">
            Baixar app
          </button>
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter" aria-labelledby="newsletter-title">
      <div class="newsletter__container">
        <div class="newsletter__content">
          <div class="newsletter__icon" aria-hidden="true">
            <picture class="newsletter__icon-picture">
              <source srcset="./LP Izy/notification.png" type="image/png">
              <img
                src="./LP Izy/notification.png"
                alt=""
                class="newsletter__icon-img"
                loading="lazy"
                decoding="async"
                width="80"
                height="80"
              >
            </picture>
          </div>

          <div class="newsletter__text">
            <h2 id="newsletter-title" class="newsletter__title">Receba novidades e promoções</h2>
            <p class="newsletter__subtitle">Fique por dentro das melhores ofertas e novidades do Izy Mercado</p>

            <form id="lead-form" class="newsletter__form" novalidate>
              <div class="newsletter__form-group">
                <label for="email" class="newsletter__label visually-hidden">Seu endereço de e-mail</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Insira seu e-mail"
                  class="newsletter__input"
                  required
                  aria-describedby="email-error"
                >
                <button type="submit" class="btn btn--primary newsletter__submit" aria-label="Inscrever-se na newsletter">
                  Receber novidades
                </button>
              </div>
              <div id="email-error" class="newsletter__error" role="alert" aria-live="polite"></div>
              <p class="newsletter__disclaimer">
                <small>Prometemos não enviar spam! Você pode cancelar a qualquer momento.</small>
              </p>
            </form>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer" role="contentinfo">
    <div class="footer__container">
      <div class="footer__content">
        <div class="footer__brand">
          <picture class="footer__logo-picture">
            <source srcset="./LP Izy/logo-izy1.png" type="image/png">
            <img
              src="./LP Izy/logo-izy1.png"
              alt="Izy Mercado"
              class="footer__logo"
              width="100"
              height="32"
              loading="lazy"
              decoding="async"
            >
          </picture>
          <p class="footer__description">Seu mercado fácil e inteligente</p>
        </div>

        <div class="footer__links">
          <nav class="footer__nav" role="navigation" aria-label="Links do rodapé">
            <ul class="footer__nav-list">
              <li><a href="#" class="footer__nav-link">Sobre nós</a></li>
              <li><a href="#" class="footer__nav-link">Como funciona</a></li>
              <li><a href="#" class="footer__nav-link">Contato</a></li>
              <li><a href="#" class="footer__nav-link">Ajuda</a></li>
            </ul>
          </nav>
        </div>

        <div class="footer__social">
          <h3 class="footer__social-title">Siga-nos</h3>
          <div class="footer__social-links">
            <a href="#" class="footer__social-link" aria-label="Instagram do Izy Mercado">
              <picture class="footer__social-picture">
                <source srcset="./LP Izy/Social, Media/Instagram.png" type="image/png">
                <img
                  src="./LP Izy/Social, Media/Instagram.png"
                  alt="Instagram"
                  class="footer__social-icon"
                  loading="lazy"
                  decoding="async"
                  width="20"
                  height="20"
                >
              </picture>
            </a>
            <a href="#" class="footer__social-link" aria-label="LinkedIn do Izy Mercado">
              <picture class="footer__social-picture">
                <source srcset="./LP Izy/Social, Media/Linkedin.png" type="image/png">
                <img
                  src="./LP Izy/Social, Media/Linkedin.png"
                  alt="LinkedIn"
                  class="footer__social-icon"
                  loading="lazy"
                  decoding="async"
                  width="20"
                  height="20"
                >
              </picture>
            </a>
          </div>
        </div>
      </div>

      <div class="footer__bottom">
        <p class="footer__copyright">© 2025, Izy Mercado. Todos os direitos reservados.</p>
        <div class="footer__legal">
          <a href="#" class="footer__legal-link">Termos de uso</a>
          <a href="#" class="footer__legal-link">Política de privacidade</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="script.js"></script>
</body>
</html>
