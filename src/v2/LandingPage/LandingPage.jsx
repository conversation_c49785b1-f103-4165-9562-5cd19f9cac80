import React from "react";
import "./style.css";
import QUERGanharR50 from "./QUER-GANHAR-r-50.svg";
import addTabButton from "./add-tab-button.svg";
import arrowIcon from "./arrow-icon.svg";
import basket2 from "./basket-2.svg";
import favicon from "./favicon.png";
import image from "./image.png";
import lock from "./lock.svg";
import moreIcon from "./more-icon.svg";
import showSideMenuButton from "./show-side-menu-button.svg";
import smallArrowDown from "./small-arrow-down.svg";
import verticalDivider from "./vertical-divider.svg";
import websiteFavicon2 from "./website-favicon-2.png";
import websiteFavicon3 from "./website-favicon-3.png";
import websiteFavicon4 from "./website-favicon-4.png";
import websiteFavicon5 from "./website-favicon-5.png";
import websiteFavicon6 from "./website-favicon-6.png";
import websiteFavicon7 from "./website-favicon-7.png";
import websiteFavicon8 from "./website-favicon-8.png";
import websiteFavicon9 from "./website-favicon-9.png";
import websiteFavicon10 from "./website-favicon-10.png";
import websiteFavicon from "./website-favicon.png";

const LandingPage = () => {
  return (
    <main className="landing-page">
      <div className="landing-page-v">
        {/* Header */}
        <header className="header">
          <div className="title-bar-buttons">
            <div className="minimize-button" />
            <div className="zoom-button" />
            <div className="close-button" />
          </div>
          <div className="element">
            <img src={showSideMenuButton} alt="Menu" className="img-3" />
            <img
              src={verticalDivider}
              alt="Divider"
              className="vertical-divider"
            />
            <img
              src={smallArrowDown}
              alt="Arrow"
              className="small-arrow-down"
            />
          </div>
          <div className="details">
            <img src={arrowIcon} alt="Arrow icon" className="img-3" />
            <div className="tabs">
              <div className="active-tab">
                <div className="domain">
                  <img src={favicon} alt="Favicon" className="img-5" />
                  <span className="domain-2">izymercado.com.br</span>
                  <img src={lock} alt="Lock" className="lock" />
                </div>
                <img src={moreIcon} alt="More" className="more-icon" />
              </div>
              <div className="other-tabs">
                {[
                  websiteFavicon,
                  websiteFavicon3,
                  websiteFavicon5,
                  websiteFavicon6,
                  websiteFavicon9,
                  websiteFavicon8,
                  websiteFavicon4,
                  websiteFavicon2,
                  websiteFavicon7,
                  image,
                  websiteFavicon10,
                ].map((icon, index) => (
                  <div className="dark-mode-collapsed" key={index}>
                    <img
                      src={icon}
                      alt="Tab icon"
                      className="website-favicon"
                    />
                  </div>
                ))}
              </div>
            </div>
            <img src={addTabButton} alt="Add tab" className="img-6" />
          </div>
        </header>

        {/* Top Menu */}
        <nav className="menu">
          <div className="logo">
            <img src={basket2} alt="Logo" className="img-2" />
            <span className="text-wrapper-31">izy</span>
          </div>
          <div className="menu-2">
            <span className="text-wrapper-32">Seja nosso parceiro</span>
            <span className="text-wrapper-32">Suporte</span>
          </div>
          <button className="button-7">
            <span className="text-wrapper-7">
              Quero fazer minhas compras com a Izy
            </span>
          </button>
        </nav>

        {/* Promo Banner */}
        <section className="card-status-pedido-10">
          <img src={QUERGanharR50} alt="Promo" className="img" />
          <p className="use-o-cupom">
            <span className="text-wrapper-28">Use o cupom </span>
            <span className="text-wrapper-29">50IZY</span>
          </p>
          <p className="text-wrapper-30">*Para compras acima de R$ 500</p>
        </section>

        {/* Main Content */}
        {/* ... The rest of the content remains unchanged for now ... */}
        {/* Due to the size, the rest of the component will be split into subcomponents in future steps for maintainability */}
      </div>
    </main>
  );
};

export default LandingPage;
