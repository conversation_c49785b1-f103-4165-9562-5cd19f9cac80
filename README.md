# Izy Mercado Landing Page v2.0

A modern, responsive, and performance-optimized landing page built with vanilla HTML, CSS, and JavaScript.

## 🚀 Features

### ✨ Modern Design
- Clean, semantic HTML5 structure
- Modern CSS architecture with CSS custom properties
- Responsive design with mobile-first approach
- Smooth animations and transitions
- Professional gradient color scheme

### 🎯 Performance Optimized
- Critical CSS inlining for faster initial render
- Lazy loading for images
- Service Worker for caching and offline functionality
- Resource hints (preload, prefetch, preconnect)
- Optimized font loading with font-display: swap
- Core Web Vitals monitoring

### ♿ Accessibility First
- WCAG 2.1 AA compliant
- Semantic HTML structure
- Proper ARIA labels and roles
- Skip links for keyboard navigation
- High contrast ratios
- Screen reader friendly

### 📱 Responsive Design
- Mobile-first CSS approach
- CSS Grid and Flexbox layouts
- Responsive images with srcset
- Touch-friendly interface
- Optimized for all device sizes

### 🔧 Technical Stack
- **HTML5**: Semantic markup with proper meta tags
- **CSS3**: Modern features including Grid, Flexbox, Custom Properties
- **JavaScript ES6+**: Modern syntax with classes and async/await
- **PWA Ready**: Web App Manifest and Service Worker
- **SEO Optimized**: Open Graph tags, structured data ready

## 📁 Project Structure

```
izy-lp/
├── index.html              # Main landing page
├── styles.css              # Main stylesheet
├── script.js               # Main JavaScript file
├── sw.js                   # Service Worker
├── manifest.json           # Web App Manifest
├── test-suite.html         # Testing and validation suite
├── README.md               # This file
├── content-categorias.svg  # Category icons
├── steps.svg               # Process steps illustration
├── title.svg               # Title/savings illustration
└── LP Izy/                 # Image assets directory
    ├── logo-izy1.png
    ├── logo.png
    ├── img1.png
    ├── img2.png
    ├── img3.png
    ├── notification.png
    └── Social, Media/
        ├── Instagram.png
        └── Linkedin.png
```

## 🛠️ Setup and Installation

### Prerequisites
- Modern web browser
- Local web server (for testing Service Worker functionality)

### Quick Start
1. Clone or download the project files
2. Serve the files using a local web server:
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Node.js (http-server)
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```
3. Open `http://localhost:8000` in your browser

### Testing
Open `test-suite.html` to run comprehensive tests:
- HTML validation
- Accessibility compliance
- Responsive design
- Performance metrics
- Functionality tests
- Image optimization
- SEO validation
- Cross-browser compatibility

## 🎨 Design System

### Colors
- **Primary**: `#B771E5` (Purple)
- **Secondary**: `#ED9292` (Pink)
- **Text Primary**: `#514D4F` (Dark Gray)
- **Text Secondary**: `#858082` (Medium Gray)
- **Background**: `#FFFFFF` (White)

### Typography
- **Font Family**: Roboto (Google Fonts)
- **Weights**: 300 (Light), 400 (Regular), 500 (Medium), 700 (Bold)
- **Responsive sizing**: Using clamp() for fluid typography

### Spacing
- **Base unit**: 1rem (16px)
- **Scale**: 0.25rem, 0.5rem, 1rem, 1.5rem, 2rem, 3rem, 4rem, 5rem, 6rem

## 📊 Performance Metrics

### Target Metrics
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **First Input Delay**: < 100ms
- **Cumulative Layout Shift**: < 0.1
- **Total Blocking Time**: < 300ms

### Optimization Techniques
- Critical CSS inlining
- Image lazy loading
- Resource preloading
- Service Worker caching
- Font optimization
- Minified assets

## 🔧 Customization

### Colors
Update CSS custom properties in `styles.css`:
```css
:root {
  --color-primary: #B771E5;
  --color-secondary: #ED9292;
  /* ... other colors */
}
```

### Content
Edit the HTML content in `index.html`:
- Update text content
- Replace images in `LP Izy/` directory
- Modify form action URL in JavaScript

### Styling
Modify styles in `styles.css`:
- Follow BEM methodology for class naming
- Use CSS custom properties for consistency
- Maintain responsive design principles

## 🧪 Testing Checklist

### Manual Testing
- [ ] Test on different devices and screen sizes
- [ ] Verify form submission functionality
- [ ] Check image loading and optimization
- [ ] Test keyboard navigation
- [ ] Verify smooth scrolling
- [ ] Test offline functionality (Service Worker)

### Automated Testing
- [ ] Run HTML validation
- [ ] Check accessibility compliance
- [ ] Verify performance metrics
- [ ] Test cross-browser compatibility
- [ ] Validate SEO elements

## 🚀 Deployment

### Production Checklist
- [ ] Minify CSS and JavaScript
- [ ] Optimize and compress images
- [ ] Configure proper HTTP headers
- [ ] Set up HTTPS
- [ ] Configure CDN if needed
- [ ] Test Service Worker functionality
- [ ] Verify analytics integration

### Recommended Hosting
- **Static hosting**: Netlify, Vercel, GitHub Pages
- **CDN**: Cloudflare, AWS CloudFront
- **Image optimization**: Cloudinary, ImageKit

## 📈 Analytics and Monitoring

### Recommended Tools
- **Google Analytics 4**: User behavior tracking
- **Google Search Console**: SEO monitoring
- **PageSpeed Insights**: Performance monitoring
- **Lighthouse**: Comprehensive auditing

### Custom Events
The JavaScript includes tracking for:
- Newsletter signups
- CTA button clicks
- Performance metrics
- Error logging

## 🤝 Contributing

### Code Style
- Use semantic HTML5 elements
- Follow BEM methodology for CSS
- Use modern JavaScript (ES6+)
- Maintain accessibility standards
- Write descriptive comments

### Pull Request Process
1. Test changes thoroughly
2. Run the test suite
3. Update documentation if needed
4. Ensure responsive design works
5. Verify accessibility compliance

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the test suite for common issues
- Review browser console for errors
- Verify all assets are loading correctly
- Test with different browsers and devices

## 🔄 Version History

### v2.0.0 (Current)
- Complete redesign with modern architecture
- Performance optimizations
- Accessibility improvements
- PWA capabilities
- Comprehensive testing suite

### v1.0.0
- Initial landing page implementation
- Basic responsive design
- Newsletter signup functionality
