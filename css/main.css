* {
  box-sizing: border-box;
}
body {
  font-size: 14px;
}
.v2106_3473 {
  width: 100%;
  height: 4690px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 36px;
  border-top-right-radius: 36px;
  border-bottom-left-radius: 36px;
  border-bottom-right-radius: 36px;
  overflow: hidden;
}
.v2106_3474 {
  width: 100%;
  height: 393px;
  background: rgba(248,248,247,1);
  padding: 32px 42px;
  margin: 48px;
  opacity: 1;
  position: absolute;
  top: 4297px;
  left: 0px;
  overflow: hidden;
}
.v2106_3475 {
  width: 100%;
  height: 62px;
  background: url("../images/v2106_3475.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 136px;
  opacity: 1;
  position: absolute;
  top: 32px;
  left: 42px;
  overflow: hidden;
}
.v2106_3476 {
  width: 769px;
  height: 62px;
  background: url("../images/v2106_3476.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3477 {
  width: 145px;
  height: 62px;
  background: url("../images/v2106_3477.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3478 {
  width: 62px;
  height: 62px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3479 {
  width: 62px;
  height: 62px;
  background: url("../images/v2106_3479.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3480 {
  width: 62px;
  height: 62px;
  background: url("../images/v2106_3480.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3481 {
  width: 62px;
  height: 62px;
  background: url("../images/v2106_3481.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3482 {
  width: 37px;
  height: 28px;
  background: url("../images/v2106_3482.png");
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 12px;
  border: 6px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3483 {
  width: 2px;
  height: 2px;
  background: url("../images/v2106_3483.png");
  opacity: 1;
  position: absolute;
  top: 49px;
  left: 43px;
  border: 6px solid rgba(133,128,130,1);
}
.v2106_3484 {
  width: 2px;
  height: 2px;
  background: url("../images/v2106_3484.png");
  opacity: 1;
  position: absolute;
  top: 49px;
  left: 21px;
  border: 6px solid rgba(133,128,130,1);
}
.v2106_3485 {
  width: 79px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 0px;
  left: 66px;
  font-family: Helvetica Neue;
  font-weight: Medium;
  font-size: 62px;
  opacity: 1;
  text-align: left;
}
.v2106_3486 {
  width: 608px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 20px;
  left: 161px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3487 {
  width: 104px;
  height: 44px;
  background: url("../images/v2106_3487.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 9px;
  left: 1092px;
  overflow: hidden;
}
.v2106_3488 {
  width: 44px;
  height: 44px;
  background: url("../images/v2106_3488.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3489 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 10px;
  left: 10px;
  overflow: hidden;
}
.v2106_3490 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3490.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3491 {
  width: 18px;
  height: 18px;
  background: url("../images/v2106_3491.png");
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3492 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3492.png");
  opacity: 1;
  position: absolute;
  top: 6px;
  left: 16px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3493 {
  width: 7px;
  height: 7px;
  background: url("../images/v2106_3493.png");
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 8px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3494 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3494.png");
  opacity: 1;
  position: absolute;
  top: 24px;
  left: 0px;
  transform: rotate(-90deg);
}
.v2106_3495 {
  width: 44px;
  height: 44px;
  background: url("../images/v2106_3495.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 60px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3496 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 10px;
  left: 10px;
  overflow: hidden;
}
.v2106_3497 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3497.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3498 {
  width: 18px;
  height: 18px;
  background: url("../images/v2106_3498.png");
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3499 {
  width: 1px;
  height: 5px;
  background: url("../images/v2106_3499.png");
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 7px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3500 {
  width: 8px;
  height: 8px;
  background: url("../images/v2106_3500.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 7px;
  left: 7px;
  overflow: hidden;
}
.v2106_3501 {
  width: 4px;
  height: 5px;
  background: url("../images/v2106_3501.png");
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 4px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3502 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3502.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3503 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3503.png");
  opacity: 1;
  position: absolute;
  top: 24px;
  left: 0px;
  transform: rotate(-90deg);
}
.v2106_3504 {
  width: 100%;
  height: 133px;
  background: url("../images/v2106_3504.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 48px;
  opacity: 1;
  position: absolute;
  top: 142px;
  left: 42px;
  overflow: hidden;
}
.v2106_3505 {
  width: 435px;
  height: 36px;
  background: url("../images/v2106_3505.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 48px;
  left: 0px;
  overflow: hidden;
}
.v2106_3506 {
  width: 435px;
  height: 36px;
  background: url("../images/v2106_3506.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3507 {
  width: 150px;
  height: 36px;
  background: url("../images/v2106_3507.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  padding: 16px 32px;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_3508 {
  width: 86px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 11px;
  left: 32px;
  font-family: SF Pro Display;
  font-weight: Medium;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3509 {
  width: 140px;
  height: 36px;
  background: url("../images/v2106_3509.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  padding: 16px 32px;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 160px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_3510 {
  width: 76px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 11px;
  left: 32px;
  font-family: SF Pro Display;
  font-weight: Medium;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3511 {
  width: 125px;
  height: 36px;
  background: url("../images/v2106_3511.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  padding: 16px 32px;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 310px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_3512 {
  width: 61px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 11px;
  left: 32px;
  font-family: SF Pro Display;
  font-weight: Medium;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3513 {
  width: 312px;
  height: 133px;
  background: url("../images/v2106_3513.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 884px;
  overflow: hidden;
}
.v2106_3514 {
  width: 148px;
  height: 133px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
}
.v2106_3515 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_3516 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3516.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3517 {
  width: 18px;
  height: 24px;
  background: url("../images/v2106_3517.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 6px;
  overflow: hidden;
}
.v2106_3518 {
  width: 18px;
  height: 16px;
  background: url("../images/v2106_3518.png");
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 0px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3519 {
  width: 5px;
  height: 3px;
  background: url("../images/v2106_3519.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 8px;
  border: 1.4999608993530273px solid rgba(81,77,79,1);
  transform: rotate(-49deg);
}
.v2106_3520 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3520.png");
  opacity: 1;
  position: absolute;
  top: 32px;
  left: 0px;
  transform: rotate(-90deg);
}
.v2106_3521 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3521.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3522 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3522.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3523 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3524 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3525 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3525.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3526 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3526.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3527 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3528 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3528.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3529 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3530 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3530.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3531 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3532 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3533 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3534 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3534.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3535 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3535.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3536 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3536.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3537 {
  width: 83px;
  height: 37px;
  background: url("../images/v2106_3537.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 80px;
  left: 16px;
  overflow: hidden;
}
.v2106_3538 {
  width: 83px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3539 {
  width: 83px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 18px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3540 {
  width: 148px;
  height: 133px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 164px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
}
.v2106_3541 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3541.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3542 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3542.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3543 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3544 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3545 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3545.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3546 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3546.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3547 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3548 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3548.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3549 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3550 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3550.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3551 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3552 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3553 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3554 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3554.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3555 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3555.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3556 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3556.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3557 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_3558 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3558.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3559 {
  width: 21px;
  height: 24px;
  background: url("../images/v2106_3559.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 5px;
  overflow: hidden;
}
.v2106_3560 {
  width: 21px;
  height: 24px;
  background: url("../images/v2106_3560.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border: 1.5px solid rgba(50,50,50,1);
}
.v2106_3561 {
  width: 13px;
  height: 16px;
  background: url("../images/v2106_3561.png");
  opacity: 1;
  position: absolute;
  top: 6px;
  left: 0px;
  border: 1.5px solid rgba(50,50,50,1);
}
.v2106_3562 {
  width: 13px;
  height: 16px;
  background: url("../images/v2106_3562.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border: 1.5px solid rgba(50,50,50,1);
}
.v2106_3563 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3563.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3564 {
  width: 85px;
  height: 37px;
  background: url("../images/v2106_3564.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 80px;
  left: 16px;
  overflow: hidden;
}
.v2106_3565 {
  width: 85px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3566 {
  width: 85px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 18px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3567 {
  width: 654px;
  height: 38px;
  background: url("../images/v2106_3567.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 323px;
  left: 313px;
  overflow: hidden;
}
.v2106_3568 {
  width: 654px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Light;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3569 {
  width: 476px;
  height: 15px;
  background: url("../images/v2106_3569.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 23px;
  left: 89px;
  overflow: hidden;
}
.v2106_3570 {
  width: 325px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Light;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3571 {
  width: 4px;
  height: 4px;
  background: rgba(229,229,228,1);
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 333px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  overflow: hidden;
}
.v2106_3572 {
  width: 131px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 0px;
  left: 345px;
  font-family: SF Pro Text;
  font-weight: Light;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3573 {
  width: 168px;
  height: 48px;
  background: url("../images/v2106_3573.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -8px;
  opacity: 1;
  position: absolute;
  top: 306px;
  left: 556px;
  overflow: hidden;
}
.v2106_3574 {
  width: 48px;
  height: 48px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: -8px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3575 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3575.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 12px;
  left: 12px;
  overflow: hidden;
}
.v2106_3576 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3576.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3577 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3577.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3578 {
  width: 4px;
  height: 8px;
  background: url("../images/v2106_3578.png");
  opacity: 1;
  position: absolute;
  top: 10px;
  left: 15px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3579 {
  width: 1px;
  height: 7px;
  background: url("../images/v2106_3579.png");
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 7px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3580 {
  width: 1px;
  height: 7px;
  background: url("../images/v2106_3580.png");
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 11px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3581 {
  width: 10px;
  height: 5px;
  background: url("../images/v2106_3581.png");
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 4px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3582 {
  width: 12px;
  height: 13px;
  background: url("../images/v2106_3582.png");
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 3px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3583 {
  width: 48px;
  height: 48px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: -8px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 40px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3584 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 12px;
  left: 12px;
  overflow: hidden;
}
.v2106_3585 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3585.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3586 {
  width: 3px;
  height: 3px;
  background: url("../images/v2106_3586.png");
  opacity: 1;
  position: absolute;
  top: 15px;
  left: 6px;
  border-radius: 50%;
}
.v2106_3587 {
  width: 18px;
  height: 17px;
  background: url("../images/v2106_3587.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 3px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3588 {
  width: 4px;
  height: 3px;
  background: url("../images/v2106_3588.png");
  opacity: 1;
  position: absolute;
  top: 21px;
  left: 18px;
  border: 1.5px solid rgba(81,77,79,1);
  transform: rotate(-180deg);
}
.v2106_3589 {
  width: 17px;
  height: 1px;
  background: url("../images/v2106_3589.png");
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 3px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3590 {
  width: 4px;
  height: 3px;
  background: url("../images/v2106_3590.png");
  opacity: 1;
  position: absolute;
  top: 12px;
  left: 11px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3591 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3591.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 24px;
  left: 24px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3592 {
  width: 48px;
  height: 48px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: -8px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 80px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3593 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 12px;
  left: 12px;
  overflow: hidden;
}
.v2106_3594 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3594.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3595 {
  width: 13px;
  height: 1px;
  background: url("../images/v2106_3595.png");
  opacity: 1;
  position: absolute;
  top: 9px;
  left: 5px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3596 {
  width: 3px;
  height: 17px;
  background: url("../images/v2106_3596.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 13px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3597 {
  width: 14px;
  height: 20px;
  background: url("../images/v2106_3597.png");
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 5px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3598 {
  width: 8px;
  height: 1px;
  background: url("../images/v2106_3598.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 8px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3599 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3599.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3600 {
  width: 48px;
  height: 48px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: -8px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 120px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3601 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 12px;
  left: 12px;
  overflow: hidden;
}
.v2106_3602 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3602.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3603 {
  width: 11px;
  height: 20px;
  background: url("../images/v2106_3603.png");
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 4px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3604 {
  width: 1px;
  height: 2px;
  background: url("../images/v2106_3604.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 11px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3605 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3605.png");
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 16px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3606 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3606.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 16px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3607 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3607.png");
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 19px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3608 {
  width: 4px;
  height: 1px;
  background: url("../images/v2106_3608.png");
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 6px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3609 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3609.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3610 {
  width: 499px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 396px;
  left: 391px;
  font-size: 36px;
  opacity: 1;
  text-align: left;
}
.v2106_3611 {
  width: 309px;
  height: 52px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  padding: 8px 32px;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 478px;
  left: 486px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_3612 {
  width: 245px;
  color: rgba(255,255,255,1);
  position: absolute;
  top: 18px;
  left: 32px;
  font-family: Roboto;
  font-weight: Medium;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v4001_407 {
  width: 800px;
  height: 533px;
  background: url("../images/v4001_407.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 608px;
  left: 240px;
  overflow: hidden;
}
.v2106_3613 {
  width: 800px;
  height: 533px;
  background: url("../images/v2106_3613.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  overflow: hidden;
}
.v2106_3678 {
  width: 408px;
  height: 66px;
  background: rgba(255,255,255,1);
  padding: 4px 16px;
  opacity: 1;
  position: absolute;
  top: 343px;
  left: 197px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_3679 {
  width: 113px;
  height: 36px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  padding: 8px 12px;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 183px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_3680 {
  width: 20px;
  height: 20px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 12px;
  overflow: hidden;
}
.v2106_3681 {
  width: 20px;
  height: 20px;
  background: url("../images/v2106_3681.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3682 {
  width: 20px;
  height: 20px;
  background: url("../images/v2106_3682.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3683 {
  width: 6px;
  height: 6px;
  background: url("../images/v2106_3683.png");
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 1px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3684 {
  width: 8px;
  height: 8px;
  background: url("../images/v2106_3684.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 10px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3685 {
  width: 0px;
  height: 0px;
  background: url("../images/v2106_3685.png");
  opacity: 1;
  position: absolute;
  top: 12px;
  left: 13px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3686 {
  width: 0px;
  height: 0px;
  background: url("../images/v2106_3686.png");
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 13px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3687 {
  width: 0px;
  height: 0px;
  background: url("../images/v2106_3687.png");
  opacity: 1;
  position: absolute;
  top: 17px;
  left: 7px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3688 {
  width: 0px;
  height: 0px;
  background: url("../images/v2106_3688.png");
  opacity: 1;
  position: absolute;
  top: 10px;
  left: 7px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3689 {
  width: 0px;
  height: 0px;
  background: url("../images/v2106_3689.png");
  opacity: 1;
  position: absolute;
  top: 9px;
  left: 4px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3690 {
  width: 0px;
  height: 0px;
  background: url("../images/v2106_3690.png");
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 4px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3691 {
  width: 6px;
  height: 6px;
  background: url("../images/v2106_3691.png");
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 5px;
  border: 1.5px solid rgba(255,255,255,1);
}
.v2106_3692 {
  width: 57px;
  color: rgba(255,255,255,1);
  position: absolute;
  top: 10px;
  left: 44px;
  font-family: SF Pro Display;
  font-weight: Medium;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3693 {
  width: 122px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 23px;
  left: 16px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3694 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 17px;
  left: 360px;
  overflow: hidden;
}
.v2106_3695 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3695.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3696 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3696.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3697 {
  width: 10px;
  height: 10px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 2px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3698 {
  width: 13px;
  height: 13px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 6px;
  left: 16px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3699 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 20px;
  left: 22px;
}
.v2106_3700 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 22px;
}
.v2106_3701 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 28px;
  left: 12px;
}
.v2106_3702 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 17px;
  left: 12px;
}
.v2106_3703 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 14px;
  left: 7px;
}
.v2106_3704 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 7px;
}
.v2106_3705 {
  width: 10px;
  height: 10px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 18px;
  left: 8px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3614 {
  width: 210px;
  height: 188px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 725px;
  left: 150px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 4px 20px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_3615 {
  width: 42px;
  height: 42px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_3616 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3616.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3617 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3617.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3618 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3618.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3619 {
  width: 25px;
  height: 19px;
  background: url("../images/v2106_3619.png");
  opacity: 1;
  position: absolute;
  top: 7px;
  left: 8px;
  border: 3px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3620 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3620.png");
  opacity: 1;
  position: absolute;
  top: 33px;
  left: 29px;
  border: 3px solid rgba(133,128,130,1);
}
.v2106_3621 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3621.png");
  opacity: 1;
  position: absolute;
  top: 33px;
  left: 14px;
  border: 3px solid rgba(133,128,130,1);
}
.v2106_3622 {
  width: 178px;
  height: 82px;
  background: url("../images/v2106_3622.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 90px;
  left: 16px;
  overflow: hidden;
}
.v2106_3623 {
  width: 178px;
  color: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3624 {
  width: 178px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 40px;
  left: 0px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3625 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3625.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3626 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3626.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3627 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3628 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3629 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3629.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3630 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3630.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3631 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3632 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3632.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3633 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3634 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3634.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3635 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3636 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3637 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3638 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3638.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3639 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3639.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3640 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3640.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3641 {
  width: 210px;
  height: 158px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 899px;
  left: 935px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 4px 20px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_3642 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3642.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_3643 {
  width: 178px;
  height: 52px;
  background: url("../images/v2106_3643.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 90px;
  left: 16px;
  overflow: hidden;
}
.v2106_3644 {
  width: 178px;
  color: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3645 {
  width: 178px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 24px;
  left: 0px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3646 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3646.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3647 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3647.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3648 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3649 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3650 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3650.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3651 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3651.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3652 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3653 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3653.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3654 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3655 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3655.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3656 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3657 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3658 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3659 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3659.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3660 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3660.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3661 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3661.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3706 {
  width: 100%;
  height: 535px;
  background: url("../images/v2106_3706.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 1289px;
  left: 115px;
  overflow: hidden;
}
.v2106_3707 {
  width: 329px;
  height: 535px;
  background: rgba(248,248,247,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
  border-bottom-left-radius: 40px;
  border-bottom-right-radius: 40px;
  overflow: hidden;
}
.v2106_3708 {
  width: 700px;
  height: 700px;
  background: url("../images/v2106_3708.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 128px;
  left: 74px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3709 {
  width: 700px;
  height: 700px;
  background: rgba(217,217,217,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3710 {
  width: 100%;
  height: 700px;
  background: url("../images/v2106_3710.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 31px;
  left: 270px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3711 {
  width: 362px;
  height: 620px;
  background: rgba(0,0,0,1);
  opacity: 1;
  position: absolute;
  top: 18px;
  left: 450px;
  transform: rotate(-7deg);
}
.v2106_3712 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3712.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3713 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3713.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 1px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3714 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3714.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
}
.v2106_3715 {
  width: 363px;
  height: 609px;
  background: url("../images/v2106_3715.png");
  opacity: 1;
  position: absolute;
  top: 14px;
  left: 452px;
  border: 1.2799999713897705px solid rgba(0,0,0,1);
  transform: rotate(-7deg);
}
.v2106_3716 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3716.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 1px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3717 {
  width: 118px;
  height: 24px;
  background: url("../images/v2106_3717.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 46px;
  left: 106px;
  overflow: hidden;
}
.v2106_3718 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3719 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3719.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3720 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3720.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3721 {
  width: 8px;
  height: 8px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 2px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3722 {
  width: 10px;
  height: 10px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 12px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3723 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 15px;
  left: 16px;
}
.v2106_3724 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 16px;
}
.v2106_3725 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 21px;
  left: 9px;
}
.v2106_3726 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 13px;
  left: 9px;
}
.v2106_3727 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 5px;
}
.v2106_3728 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 5px;
}
.v2106_3729 {
  width: 8px;
  height: 8px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 13px;
  left: 6px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3730 {
  width: 86px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 2px;
  left: 32px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3731 {
  width: 329px;
  height: 535px;
  background: rgba(248,248,247,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 361px;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
  border-bottom-left-radius: 40px;
  border-bottom-right-radius: 40px;
  overflow: hidden;
}
.v2106_3732 {
  width: 700px;
  height: 700px;
  background: url("../images/v2106_3732.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 128px;
  left: 156px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3733 {
  width: 700px;
  height: 700px;
  background: rgba(217,217,217,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3734 {
  width: 100%;
  height: 700px;
  background: url("../images/v2106_3734.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 31px;
  left: 271px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3735 {
  width: 362px;
  height: 620px;
  background: rgba(0,0,0,1);
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 450px;
  transform: rotate(-7deg);
}
.v2106_3736 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3736.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3737 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3737.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3738 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3738.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
}
.v2106_3739 {
  width: 363px;
  height: 609px;
  background: url("../images/v2106_3739.png");
  opacity: 1;
  position: absolute;
  top: 15px;
  left: 452px;
  border: 1.2799999713897705px solid rgba(0,0,0,1);
  transform: rotate(-7deg);
}
.v2106_3740 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3740.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3741 {
  width: 200px;
  height: 24px;
  background: url("../images/v2106_3741.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 46px;
  left: 65px;
  overflow: hidden;
}
.v2106_3742 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3743 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3743.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3744 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3744.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3745 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3745.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3746 {
  width: 14px;
  height: 11px;
  background: url("../images/v2106_3746.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 5px;
  border: 2px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3747 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3747.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 16px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_3748 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3748.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 8px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_3749 {
  width: 168px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 2px;
  left: 32px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3750 {
  width: 329px;
  height: 535px;
  background: rgba(248,248,247,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 722px;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
  border-bottom-left-radius: 40px;
  border-bottom-right-radius: 40px;
  overflow: hidden;
}
.v2106_3751 {
  width: 700px;
  height: 700px;
  background: url("../images/v2106_3751.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 128px;
  left: 156px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3752 {
  width: 700px;
  height: 700px;
  background: rgba(217,217,217,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3753 {
  width: 100%;
  height: 700px;
  background: url("../images/v2106_3753.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 31px;
  left: 271px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3754 {
  width: 362px;
  height: 620px;
  background: rgba(0,0,0,1);
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 450px;
  transform: rotate(-7deg);
}
.v2106_3755 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3755.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3756 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3756.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3757 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3757.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
}
.v2106_3758 {
  width: 363px;
  height: 609px;
  background: url("../images/v2106_3758.png");
  opacity: 1;
  position: absolute;
  top: 15px;
  left: 452px;
  border: 1.2799999713897705px solid rgba(0,0,0,1);
  transform: rotate(-7deg);
}
.v2106_3759 {
  width: 100%;
  height: 699px;
  background: url("../images/v2106_3759.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  transform: rotate(-7deg);
  overflow: hidden;
}
.v2106_3760 {
  width: 234px;
  height: 24px;
  background: url("../images/v2106_3760.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 46px;
  left: 48px;
  overflow: hidden;
}
.v2106_3761 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3761.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3762 {
  width: 202px;
  color: url("../images/v2106_3762.png");
  position: absolute;
  top: 2px;
  left: 32px;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3763 {
  width: 312px;
  height: 133px;
  background: url("../images/v2106_3763.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 1977px;
  left: 846px;
  overflow: hidden;
}
.v2106_3764 {
  width: 148px;
  height: 133px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
}
.v2106_3765 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_3766 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3766.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3767 {
  width: 18px;
  height: 24px;
  background: url("../images/v2106_3767.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 6px;
  overflow: hidden;
}
.v2106_3768 {
  width: 18px;
  height: 16px;
  background: url("../images/v2106_3768.png");
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 0px;
  border: 1.5px solid rgba(81,77,79,1);
}
.v2106_3769 {
  width: 5px;
  height: 3px;
  background: url("../images/v2106_3769.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 8px;
  border: 1.4999608993530273px solid rgba(81,77,79,1);
  transform: rotate(-49deg);
}
.v2106_3770 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3770.png");
  opacity: 1;
  position: absolute;
  top: 32px;
  left: 0px;
  transform: rotate(-90deg);
}
.v2106_3787 {
  width: 83px;
  height: 37px;
  background: url("../images/v2106_3787.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 80px;
  left: 16px;
  overflow: hidden;
}
.v2106_3788 {
  width: 83px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3789 {
  width: 83px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 18px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3790 {
  width: 148px;
  height: 133px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 164px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
}
.v2106_3807 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_3808 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3808.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3809 {
  width: 21px;
  height: 24px;
  background: url("../images/v2106_3809.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 5px;
  overflow: hidden;
}
.v2106_3810 {
  width: 21px;
  height: 24px;
  background: url("../images/v2106_3810.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border: 1.5px solid rgba(50,50,50,1);
}
.v2106_3811 {
  width: 13px;
  height: 16px;
  background: url("../images/v2106_3811.png");
  opacity: 1;
  position: absolute;
  top: 6px;
  left: 0px;
  border: 1.5px solid rgba(50,50,50,1);
}
.v2106_3812 {
  width: 13px;
  height: 16px;
  background: url("../images/v2106_3812.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border: 1.5px solid rgba(50,50,50,1);
}
.v2106_3813 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3813.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3814 {
  width: 85px;
  height: 37px;
  background: url("../images/v2106_3814.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 80px;
  left: 16px;
  overflow: hidden;
}
.v2106_3815 {
  width: 85px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3816 {
  width: 85px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 18px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3817 {
  width: 641px;
  height: 111px;
  background: url("../images/v2106_3817.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 2449px;
  left: 320px;
  overflow: hidden;
}
.name {
  color: #fff;
}
.v2106_3819 {
  width: 641px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 71px;
  left: 0px;
  font-size: 36px;
  opacity: 1;
  text-align: left;
}
.v2106_3820 {
  width: 470px;
  height: 138px;
  background: url("../images/v2106_3820.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 1972px;
  left: 121px;
  overflow: hidden;
}
.v2106_3821 {
  width: 42px;
  height: 42px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3822 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3822.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3823 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3823.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3824 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3824.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3825 {
  width: 25px;
  height: 19px;
  background: url("../images/v2106_3825.png");
  opacity: 1;
  position: absolute;
  top: 7px;
  left: 8px;
  border: 3px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3826 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3826.png");
  opacity: 1;
  position: absolute;
  top: 33px;
  left: 29px;
  border: 3px solid rgba(133,128,130,1);
}
.v2106_3827 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3827.png");
  opacity: 1;
  position: absolute;
  top: 33px;
  left: 14px;
  border: 3px solid rgba(133,128,130,1);
}
.v2106_3828 {
  width: 470px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 58px;
  left: 0px;
  font-family: Roboto;
  font-weight: ExtraLight;
  font-size: 36px;
  opacity: 1;
  text-align: left;
}
.v2106_3830 {
  width: 600px;
  height: 400px;
  background: url("../images/v2106_3830.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3292px;
  left: 375px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  overflow: hidden;
}
.v4001_408 {
  width: 600px;
  height: 400px;
  background: url("../images/v4001_408.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 2744px;
  left: 529px;
  overflow: hidden;
}
.v2106_3829 {
  width: 600px;
  height: 400px;
  background: url("../images/v2106_3829.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  overflow: hidden;
}
.v2106_3890 {
  width: 358px;
  height: 114px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 257px;
  left: 102px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 4px 20px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_3891 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3891.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3892 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3892.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3893 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3894 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3895 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3895.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3896 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3896.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3897 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3898 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3898.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3899 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3900 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3900.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3901 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3902 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3903 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3904 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3904.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3905 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3905.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3906 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3906.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3907 {
  width: 326px;
  height: 82px;
  background: url("../images/v2106_3907.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
}
.v2106_3908 {
  width: 326px;
  height: 32px;
  background: url("../images/v2106_3908.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3909 {
  width: 180px;
  height: 32px;
  background: url("../images/v2106_3909.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3910 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
}
.v2106_3911 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3911.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3912 {
  width: 132px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 9px;
  left: 48px;
  font-family: SF Pro Text;
  font-weight: Medium;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3913 {
  width: 93px;
  height: 16px;
  background: url("../images/v2106_3913.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 6px;
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 233px;
  overflow: hidden;
}
.v2106_3914 {
  width: 16px;
  height: 16px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3915 {
  width: 16px;
  height: 16px;
  background: url("../images/v2106_3915.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3916 {
  width: 16px;
  height: 16px;
  background: url("../images/v2106_3916.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3917 {
  width: 5px;
  height: 5px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 1px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3918 {
  width: 6px;
  height: 6px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 8px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3919 {
  width: 0px;
  height: 0px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 10px;
  left: 11px;
}
.v2106_3920 {
  width: 0px;
  height: 0px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 11px;
}
.v2106_3921 {
  width: 0px;
  height: 0px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 14px;
  left: 6px;
}
.v2106_3922 {
  width: 0px;
  height: 0px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 6px;
}
.v2106_3923 {
  width: 0px;
  height: 0px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 7px;
  left: 3px;
}
.v2106_3924 {
  width: 0px;
  height: 0px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 3px;
}
.v2106_3925 {
  width: 5px;
  height: 5px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 9px;
  left: 4px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3926 {
  width: 71px;
  color: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  position: absolute;
  top: 2px;
  left: 22px;
  font-family: SF Pro Text;
  font-weight: Medium;
  font-size: 10px;
  opacity: 1;
  text-align: left;
}
.v2106_3927 {
  width: 326px;
  height: 38px;
  background: url("../images/v2106_3927.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 44px;
  left: 0px;
  overflow: hidden;
}
.v2106_3928 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3928.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3929 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3930 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3930.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3931 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3932 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3932.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3933 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3934 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3935 {
  width: 95px;
  height: 33px;
  background: url("../images/v2106_3935.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 231px;
  overflow: hidden;
}
.v2106_3936 {
  width: 95px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 10px;
  opacity: 1;
  text-align: left;
}
.v2106_3937 {
  width: 95px;
  height: 18px;
  background: url("../images/v2106_3937.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 15px;
  left: 0px;
  overflow: hidden;
}
.v2106_3938 {
  width: 77px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Display;
  font-weight: Bold;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3939 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 81px;
  transform: rotate(-90deg);
  overflow: hidden;
}
.v2106_3940 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3940.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3941 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3941.png");
  opacity: 1;
  position: absolute;
  top: 6px;
  left: 4px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3942 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3942.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3858 {
  width: 221px;
  height: 174px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 2784px;
  left: 982px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 4px 20px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_3859 {
  width: 42px;
  height: 42px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_3860 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3860.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3861 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3861.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3862 {
  width: 14px;
  height: 14px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 3px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3863 {
  width: 17px;
  height: 17px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 8px;
  left: 21px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3864 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 28px;
}
.v2106_3865 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 7px;
  left: 28px;
}
.v2106_3866 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 36px;
  left: 16px;
}
.v2106_3867 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 22px;
  left: 16px;
}
.v2106_3868 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 9px;
}
.v2106_3869 {
  width: 1px;
  height: 1px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 9px;
}
.v2106_3870 {
  width: 14px;
  height: 14px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  opacity: 1;
  position: absolute;
  top: 23px;
  left: 10px;
  border: 1.5px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3871 {
  width: 189px;
  height: 68px;
  background: url("../images/v2106_3871.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 90px;
  left: 16px;
  overflow: hidden;
}
.v2106_3872 {
  width: 189px;
  color: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3873 {
  width: 189px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 40px;
  left: 0px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3874 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3874.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3875 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3875.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3876 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3877 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3878 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3878.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3879 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3879.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3880 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3881 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3881.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3882 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3883 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3883.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3884 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3885 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3886 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3887 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3887.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3888 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3888.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3889 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3889.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3831 {
  width: 219px;
  height: 174px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 2708px;
  left: 437px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 4px 20px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_3832 {
  width: 42px;
  height: 42px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_3833 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3833.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3834 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3834.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3835 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_3835.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3836 {
  width: 25px;
  height: 19px;
  background: url("../images/v2106_3836.png");
  opacity: 1;
  position: absolute;
  top: 7px;
  left: 8px;
  border: 3px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3837 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3837.png");
  opacity: 1;
  position: absolute;
  top: 33px;
  left: 29px;
  border: 3px solid rgba(133,128,130,1);
}
.v2106_3838 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3838.png");
  opacity: 1;
  position: absolute;
  top: 33px;
  left: 14px;
  border: 3px solid rgba(133,128,130,1);
}
.v2106_3839 {
  width: 187px;
  height: 68px;
  background: url("../images/v2106_3839.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 90px;
  left: 16px;
  overflow: hidden;
}
.v2106_3840 {
  width: 187px;
  color: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3841 {
  width: 187px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 40px;
  left: 0px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3842 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3842.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3843 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3843.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3844 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3845 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3846 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3846.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3847 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3847.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3848 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3849 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3849.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3850 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3851 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3851.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3852 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3853 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3854 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3855 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3855.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3856 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3856.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3857 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3857.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3943 {
  width: 318px;
  height: 244px;
  background: url("../images/v2106_3943.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 2822px;
  left: 96px;
  overflow: hidden;
}
.v2106_3944 {
  width: 318px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Thin;
  font-size: 36px;
  opacity: 1;
  text-align: left;
}
.v2106_3945 {
  width: 199px;
  height: 52px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  padding: 8px 32px;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 192px;
  left: 0px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_3946 {
  width: 135px;
  color: rgba(255,255,255,1);
  position: absolute;
  top: 18px;
  left: 32px;
  font-family: Roboto;
  font-weight: Medium;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_3947 {
  width: 100%;
  height: 143px;
  background: url("../images/v2106_3947.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 2158px;
  left: 121px;
  overflow: hidden;
}
.v2106_3948 {
  width: 182px;
  height: 143px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 2px 10px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_3949 {
  width: 56px;
  height: 55px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3950 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 12px;
  overflow: hidden;
}
.v2106_3951 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3951.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_3952 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3952.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 1px;
  overflow: hidden;
}
.v2106_3953 {
  width: 4px;
  height: 4px;
  background: url("../images/v2106_3953.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 26px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_3954 {
  width: 2px;
  height: 5px;
  background: url("../images/v2106_3954.png");
  opacity: 1;
  position: absolute;
  top: 9px;
  left: 19px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_3955 {
  width: 2px;
  height: 5px;
  background: url("../images/v2106_3955.png");
  opacity: 1;
  position: absolute;
  top: 9px;
  left: 25px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_3956 {
  width: 12px;
  height: 1px;
  background: url("../images/v2106_3956.png");
  opacity: 1;
  position: absolute;
  top: 14px;
  left: 2px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_3957 {
  width: 4px;
  height: 16px;
  background: url("../images/v2106_3957.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 14px;
  border: 2px solid rgba(81,77,79,1);
  border-top-left-radius: 1px;
  border-top-right-radius: 1px;
  border-bottom-left-radius: 1px;
  border-bottom-right-radius: 1px;
  overflow: hidden;
}
.v2106_3958 {
  width: 24px;
  height: 13px;
  background: url("../images/v2106_3958.png");
  opacity: 1;
  position: absolute;
  top: 14px;
  left: 4px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_3959 {
  width: 10px;
  height: 8px;
  background: url("../images/v2106_3959.png");
  opacity: 1;
  position: absolute;
  top: 6px;
  left: 4px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_3960 {
  width: 12px;
  height: 1px;
  background: url("../images/v2106_3960.png");
  opacity: 1;
  position: absolute;
  top: 14px;
  left: 18px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_3961 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3961.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3962 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3962.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3963 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3964 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3965 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3965.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3966 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3966.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3967 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3968 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3968.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3969 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3970 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3970.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3971 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3972 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_3973 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_3974 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3974.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3975 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_3975.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_3976 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_3976.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3977 {
  width: 119px;
  height: 24px;
  background: url("../images/v2106_3977.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 103px;
  left: 16px;
  overflow: hidden;
}
.v2106_3978 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3979 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3979.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3980 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3980.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3981 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3981.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_3982 {
  width: 14px;
  height: 11px;
  background: url("../images/v2106_3982.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 5px;
  border: 2px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_3983 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3983.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 16px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_3984 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_3984.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 8px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_3985 {
  width: 91px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 4px;
  left: 28px;
  font-family: Helvetica Neue;
  font-weight: Medium;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_3986 {
  width: 182px;
  height: 143px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 214px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 2px 10px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_3987 {
  width: 56px;
  height: 55px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.name {
  color: #fff;
}
.v2106_3989 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_3989.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_3990 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_3990.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3991 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_3992 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_3993 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_3993.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_3994 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_3994.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_3995 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3996 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_3996.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_3997 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_3998 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_3998.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_3999 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4000 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_4001 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_4002 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_4002.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4003 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_4003.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_4004 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_4004.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4005 {
  width: 119px;
  height: 24px;
  background: url("../images/v2106_4005.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 103px;
  left: 16px;
  overflow: hidden;
}
.v2106_4006 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4007 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4007.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4008 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4008.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4009 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4009.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4010 {
  width: 14px;
  height: 11px;
  background: url("../images/v2106_4010.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 5px;
  border: 2px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_4011 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4011.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 16px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_4012 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4012.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 8px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_4013 {
  width: 91px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 4px;
  left: 28px;
  font-family: Helvetica Neue;
  font-weight: Medium;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_4014 {
  width: 182px;
  height: 143px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 428px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 2px 10px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_4015 {
  width: 56px;
  height: 55px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4016 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 12px;
  overflow: hidden;
}
.v2106_4017 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4017.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_4018 {
  width: 4px;
  height: 4px;
  background: url("../images/v2106_4018.png");
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 13px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4019 {
  width: 4px;
  height: 4px;
  background: url("../images/v2106_4019.png");
  opacity: 1;
  position: absolute;
  top: 17px;
  left: 13px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4020 {
  width: 21px;
  height: 21px;
  background: url("../images/v2106_4020.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 5px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4021 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4021.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4022 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_4022.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_4023 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_4023.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4024 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_4025 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_4026 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_4026.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_4027 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_4027.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4028 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4029 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4029.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_4030 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4031 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4031.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_4032 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4033 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_4034 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_4035 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_4035.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4036 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_4036.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_4037 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_4037.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4038 {
  width: 110px;
  height: 24px;
  background: url("../images/v2106_4038.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 103px;
  left: 16px;
  overflow: hidden;
}
.v2106_4039 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4040 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4040.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4041 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4041.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4042 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4042.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4043 {
  width: 14px;
  height: 11px;
  background: url("../images/v2106_4043.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 5px;
  border: 2px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_4044 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4044.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 16px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_4045 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4045.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 8px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_4046 {
  width: 82px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 4px;
  left: 28px;
  font-family: Helvetica Neue;
  font-weight: Medium;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_4047 {
  width: 182px;
  height: 143px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 642px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 2px 10px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_4048 {
  width: 56px;
  height: 55px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4049 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 12px;
  overflow: hidden;
}
.v2106_4050 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4050.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_4051 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4051.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4052 {
  width: 26px;
  height: 13px;
  background: url("../images/v2106_4052.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 2px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4053 {
  width: 1px;
  height: 4px;
  background: url("../images/v2106_4053.png");
  opacity: 1;
  position: absolute;
  top: 10px;
  left: 19px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4054 {
  width: 1px;
  height: 4px;
  background: url("../images/v2106_4054.png");
  opacity: 1;
  position: absolute;
  top: 10px;
  left: 13px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4055 {
  width: 26px;
  height: 15px;
  background: url("../images/v2106_4055.png");
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 2px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4056 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_4056.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_4057 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_4057.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4058 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_4059 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_4060 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_4060.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_4061 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_4061.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4062 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4063 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4063.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_4064 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4065 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4065.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_4066 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4067 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_4068 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_4069 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_4069.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4070 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_4070.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_4071 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_4071.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4072 {
  width: 106px;
  height: 24px;
  background: url("../images/v2106_4072.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 103px;
  left: 16px;
  overflow: hidden;
}
.v2106_4073 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4074 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4074.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4075 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4075.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4076 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4076.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4077 {
  width: 14px;
  height: 11px;
  background: url("../images/v2106_4077.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 5px;
  border: 2px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_4078 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4078.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 16px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_4079 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4079.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 8px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_4080 {
  width: 78px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 4px;
  left: 28px;
  font-family: Helvetica Neue;
  font-weight: Medium;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_4081 {
  width: 182px;
  height: 143px;
  background: rgba(255,255,255,1);
  padding: 16px 16px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 856px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 2px 10px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.10000000149011612);
  overflow: hidden;
}
.v2106_4082 {
  width: 56px;
  height: 55px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4083 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 11px;
  left: 12px;
  overflow: hidden;
}
.v2106_4084 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4084.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 1px;
  left: 1px;
  overflow: hidden;
}
.v2106_4085 {
  width: 7px;
  height: 3px;
  background: url("../images/v2106_4085.png");
  opacity: 1;
  position: absolute;
  top: 15px;
  left: 20px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4086 {
  width: 18px;
  height: 26px;
  background: url("../images/v2106_4086.png");
  opacity: 1;
  position: absolute;
  top: 2px;
  left: 3px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4087 {
  width: 8px;
  height: 1px;
  background: url("../images/v2106_4087.png");
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 12px;
  border: 2px solid rgba(81,77,79,1);
}
.v2106_4088 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4088.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4105 {
  width: 72px;
  height: 24px;
  background: url("../images/v2106_4105.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 103px;
  left: 16px;
  overflow: hidden;
}
.v2106_4106 {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4107 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4107.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4108 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4108.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4109 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4109.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4110 {
  width: 14px;
  height: 11px;
  background: url("../images/v2106_4110.png");
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 5px;
  border: 2px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_4111 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4111.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 16px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_4112 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4112.png");
  opacity: 1;
  position: absolute;
  top: 19px;
  left: 8px;
  border: 2px solid rgba(133,128,130,1);
}
.v2106_4113 {
  width: 44px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 4px;
  left: 28px;
  font-family: Helvetica Neue;
  font-weight: Medium;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_4114 {
  width: 306px;
  height: 98px;
  background: url("../images/v2106_4114.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 3903px;
  left: 186px;
  overflow: hidden;
}
.v2106_4115 {
  width: 42px;
  height: 42px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4116 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_4116.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4117 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_4117.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4118 {
  width: 42px;
  height: 42px;
  background: url("../images/v2106_4118.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4119 {
  width: 31px;
  height: 27px;
  background: url("../images/v2106_4119.png");
  opacity: 1;
  position: absolute;
  top: 7px;
  left: 5px;
  border: 2px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_4120 {
  width: 306px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 58px;
  left: 0px;
  font-family: Roboto;
  font-weight: ExtraLight;
  font-size: 36px;
  opacity: 1;
  text-align: left;
}
.v2106_4121 {
  width: 309px;
  height: 52px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  padding: 8px 32px;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 4033px;
  left: 186px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_4122 {
  width: 245px;
  color: rgba(255,255,255,1);
  position: absolute;
  top: 18px;
  left: 32px;
  font-family: Roboto;
  font-weight: Medium;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.name {
  color: #fff;
}
.v2106_4124 {
  width: 272px;
  height: 125px;
  background: rgba(248,248,247,1);
  padding: 16px 16px;
  margin: 16px;
  opacity: 1;
  position: absolute;
  top: 3515px;
  left: 782px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  box-shadow: 0px 4px 20px rgba(0.3176470696926117, 0.3019607961177826, 0.30980393290519714, 0.05000000074505806);
  overflow: hidden;
}
.v2106_4125 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4125.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 16px;
  left: 16px;
  overflow: hidden;
}
.v2106_4126 {
  width: 240px;
  height: 53px;
  background: url("../images/v2106_4126.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 8px;
  opacity: 1;
  position: absolute;
  top: 56px;
  left: 16px;
  overflow: hidden;
}
.v2106_4127 {
  width: 240px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Display;
  font-weight: Bold;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
.v2106_4128 {
  width: 240px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 25px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.name {
  color: #fff;
}
.v2106_4130 {
  width: 100%;
  height: 76px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  padding: 24px 42px;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 152px;
  left: 0px;
  overflow: hidden;
}
.v2106_4131 {
  width: 318px;
  height: 38px;
  background: url("../images/v2106_4131.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 15px;
  opacity: 1;
  position: absolute;
  top: 75px;
  left: 16px;
  overflow: hidden;
}
.v2106_4132 {
  width: 127px;
  height: 38px;
  background: url("../images/v2106_4132.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4133 {
  width: 113px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 11px;
  opacity: 1;
  text-align: left;
}
.v2106_4134 {
  width: 127px;
  color: rgba(81,77,79,1);
  position: absolute;
  top: 16px;
  left: 0px;
  font-family: SF Pro Text;
  font-weight: Regular;
  font-size: 20px;
  opacity: 1;
  text-align: left;
}
.v2106_4135 {
  width: 108px;
  height: 38px;
  background: url("../images/v2106_4135.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 210px;
  overflow: hidden;
}
.v2106_4136 {
  width: 90px;
  height: 38px;
  background: url("../images/v2106_4136.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: -6px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4137 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 0px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4138 {
  width: 24px;
  height: 24px;
  background: url("../images/v2106_4138.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 4px;
  left: 4px;
  overflow: hidden;
}
.v2106_4139 {
  width: 38px;
  height: 38px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 26px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4140 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4140.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 3px;
  overflow: hidden;
}
.v2106_4141 {
  width: 32px;
  height: 32px;
  background: rgba(248,248,247,1);
  padding: 10px 10px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 3px;
  left: 58px;
  border-top-left-radius: 42px;
  border-top-right-radius: 42px;
  border-bottom-left-radius: 42px;
  border-bottom-right-radius: 42px;
  overflow: hidden;
}
.v2106_4142 {
  width: 14px;
  color: rgba(102,102,102,1);
  position: absolute;
  top: 9px;
  left: 9px;
  font-family: SF Pro Display;
  font-weight: Regular;
  font-size: 12px;
  opacity: 1;
  text-align: left;
}
.v2106_4143 {
  width: 14px;
  height: 14px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 26px;
  left: 108px;
  transform: rotate(-180deg);
  overflow: hidden;
}
.v2106_4144 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_4144.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4145 {
  width: 2px;
  height: 4px;
  background: url("../images/v2106_4145.png");
  opacity: 1;
  position: absolute;
  top: 5px;
  left: 6px;
  border: 1.5px solid rgba(158,158,158,1);
}
.v2106_4146 {
  width: 14px;
  height: 14px;
  background: url("../images/v2106_4146.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4147 {
  width: 237px;
  color: rgba(255,255,255,1);
  position: absolute;
  top: 24px;
  left: 42px;
  font-size: 24px;
  opacity: 1;
  text-align: left;
}
.v2106_4148 {
  width: 210px;
  color: rgba(255,255,255,1);
  position: absolute;
  top: 24px;
  left: 540px;
  font-size: 24px;
  opacity: 1;
  text-align: left;
}
.v2106_4149 {
  width: 227px;
  color: rgba(255,255,255,1);
  position: absolute;
  top: 29px;
  left: 1011px;
  font-family: Roboto;
  font-weight: Light;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.name {
  color: #fff;
}
.v2106_4151 {
  width: 100%;
  height: 100px;
  background: rgba(255,255,255,0.8999999761581421);
  padding: 24px 42px;
  margin: 10px;
  opacity: 1;
  position: absolute;
  top: 52px;
  left: 0px;
  overflow: hidden;
}
.v2106_4152 {
  width: 309px;
  height: 32px;
  background: url("../images/v2106_4152.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 4px;
  opacity: 1;
  position: absolute;
  top: 34px;
  left: 42px;
  overflow: hidden;
}
.v2106_4153 {
  width: 32px;
  height: 32px;
  background: rgba(255,255,255,1);
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4154 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4154.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4155 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4155.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
}
.v2106_4156 {
  width: 32px;
  height: 32px;
  background: url("../images/v2106_4156.png");
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
}
.v2106_4157 {
  width: 19px;
  height: 14px;
  background: url("../images/v2106_4157.png");
  opacity: 1;
  position: absolute;
  top: 6px;
  left: 6px;
  border: 3px solid linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
}
.v2106_4158 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4158.png");
  opacity: 1;
  position: absolute;
  top: 25px;
  left: 22px;
  border: 3px solid rgba(133,128,130,1);
}
.v2106_4159 {
  width: 1px;
  height: 1px;
  background: url("../images/v2106_4159.png");
  opacity: 1;
  position: absolute;
  top: 25px;
  left: 11px;
  border: 3px solid rgba(133,128,130,1);
}
.v2106_4160 {
  width: 41px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 0px;
  left: 36px;
  font-family: Helvetica Neue;
  font-weight: Medium;
  font-size: 32px;
  opacity: 1;
  text-align: left;
}
.v2106_4161 {
  width: 229px;
  height: 19px;
  background: url("../images/v2106_4161.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  margin: 32px;
  opacity: 1;
  position: absolute;
  top: 40px;
  left: 525px;
  overflow: hidden;
}
.v2106_4162 {
  width: 141px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 0px;
  left: 0px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_4163 {
  width: 56px;
  color: rgba(133,128,130,1);
  position: absolute;
  top: 0px;
  left: 173px;
  font-family: Roboto;
  font-weight: Regular;
  font-size: 16px;
  opacity: 1;
  text-align: left;
}
.v2106_4164 {
  width: 309px;
  height: 52px;
  background: linear-gradient(rgba(183,113,229,1), rgba(236,145,145,1));
  padding: 8px 32px;
  margin: 12px;
  opacity: 1;
  position: absolute;
  top: 24px;
  left: 929px;
  border-top-left-radius: 52px;
  border-top-right-radius: 52px;
  border-bottom-left-radius: 52px;
  border-bottom-right-radius: 52px;
  overflow: hidden;
}
.v2106_4165 {
  width: 245px;
  color: rgba(255,255,255,1);
  position: absolute;
  top: 18px;
  left: 32px;
  font-family: Roboto;
  font-weight: Medium;
  font-size: 14px;
  opacity: 1;
  text-align: left;
}
