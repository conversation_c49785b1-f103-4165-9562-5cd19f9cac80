/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  color: #514d4f;
  line-height: 1.6;
}

/* Header */
.menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.text-wrapper-9 {
  font-size: 2rem;
  font-weight: 500;
  color: #858082;
}

.menu-2 {
  display: flex;
  gap: 1rem;
}

.text-wrapper-10 {
  color: #858082;
  text-decoration: none;
}

/* Buttons */
.button-4 {
  background: linear-gradient(90deg, rgba(183, 113, 229, 1) 0%, rgba(237, 146, 146, 1) 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  cursor: pointer;
}

/* Hero Section */
.hero {
  text-align: center;
  padding: 5rem 1rem;
  margin-top: 4rem;
}

.seu-mercado-f-cil-e {
  font-size: 2.25rem;
  margin-bottom: 2rem;
}

.span {
  font-weight: 300;
}

.text-wrapper-2 {
  font-weight: 500;
}

/* Features Section */
.features {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  padding: 2rem;
}

.overlap-wrapper, .card {
  background-color: #f8f8f7;
  border-radius: 1rem;
  overflow: hidden;
  max-width: 100%;
}

.overlap-wrapper img, .card img {
  max-width: 100%;
  height: auto;
}

/* Steps Section */
.steps {
  padding: 2rem;
  text-align: center;
}

.content-3 {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 2rem;
}

.title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Actions Section */
.actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
}

.button-3 {
  border: 1px solid #eaeaea;
  background-color: white;
  color: #514d4f;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  cursor: pointer;
}

/* Newsletter Section */
.content-mail {
  background-color: #f8f8f7;
  padding: 2rem;
  text-align: center;
}

.content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.button {
  display: flex;
  border: 1px solid #e6e6e6;
  border-radius: 2rem;
  overflow: hidden;
}

.text-wrapper-3 {
  border: none;
  padding: 0.5rem 1rem;
  flex-grow: 1;
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f8f7;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu {
    flex-direction: column;
    gap: 1rem;
  }

  .features {
    flex-direction: column;
  }

  .content-3 {
    flex-direction: column;
  }

  .actions {
    flex-direction: column;
  }

  .footer {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
