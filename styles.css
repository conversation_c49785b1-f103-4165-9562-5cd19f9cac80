/* ==========================================================================
   Izy Mercado Landing Page v2.0 - Modern CSS Architecture
   ========================================================================== */

/* CSS Custom Properties (CSS Variables) */
:root {
  /* Colors */
  --color-primary: #B771E5;
  --color-primary-light: #C88AE8;
  --color-primary-dark: #9A5BC7;
  --color-secondary: #ED9292;
  --color-secondary-light: #F2A8A8;
  --color-secondary-dark: #E67B7B;

  --color-text-primary: #514D4F;
  --color-text-secondary: #858082;
  --color-text-light: #A8A5A6;
  --color-text-white: #FFFFFF;

  --color-background-primary: #FFFFFF;
  --color-background-secondary: #F8F8F7;
  --color-background-tertiary: #F5F5F4;

  --color-border-light: #E6E6E6;
  --color-border-medium: #EAEAEA;
  --color-border-dark: #D1D1D1;

  --color-success: #10B981;
  --color-error: #EF4444;
  --color-warning: #F59E0B;

  /* Gradients */
  --gradient-primary: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  --gradient-primary-hover: linear-gradient(90deg, var(--color-primary-dark) 0%, var(--color-secondary-dark) 100%);

  /* Typography */
  --font-family-primary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  /* Spacing */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  --spacing-4xl: 5rem;     /* 80px */
  --spacing-5xl: 6rem;     /* 96px */

  /* Border radius */
  --border-radius-sm: 0.25rem;   /* 4px */
  --border-radius-md: 0.5rem;    /* 8px */
  --border-radius-lg: 1rem;      /* 16px */
  --border-radius-xl: 1.5rem;    /* 24px */
  --border-radius-2xl: 2rem;     /* 32px */
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Improve text rendering */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

p {
  line-height: var(--line-height-relaxed);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Picture element support */
picture {
  display: block;
}

picture img {
  width: 100%;
  height: auto;
}

/* Image loading states */
img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

img[loading="lazy"].loaded {
  opacity: 1;
}

/* Fallback for browsers that don't support loading="lazy" */
.no-loading-lazy img[loading="lazy"] {
  opacity: 1;
}

button {
  font-family: inherit;
  cursor: pointer;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Skip link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-text-primary);
  color: var(--color-text-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  z-index: var(--z-tooltip);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Visually hidden utility class */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* ==========================================================================
   Button Components
   ========================================================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  text-align: center;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--border-radius-full);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button sizes */
.btn--small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn--medium {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
}

.btn--large {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-lg);
}

/* Button variants */
.btn--primary {
  background: var(--gradient-primary);
  color: var(--color-text-white);
  border-color: transparent;
  box-shadow: var(--shadow-md);
}

.btn--primary:hover:not(:disabled) {
  background: var(--gradient-primary-hover);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn--primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.btn--secondary {
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  border-color: var(--color-border-medium);
}

.btn--secondary:hover:not(:disabled) {
  background: var(--color-background-secondary);
  border-color: var(--color-border-dark);
  box-shadow: var(--shadow-md);
}

.btn--outline {
  background: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn--outline:hover:not(:disabled) {
  background: var(--color-primary);
  color: var(--color-text-white);
}

/* Button icon */
.btn__icon {
  font-size: 1.2em;
}

/* ==========================================================================
   Layout Utilities
   ========================================================================== */

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.container--small {
  max-width: 800px;
}

.container--large {
  max-width: 1400px;
}

/* Grid system */
.grid {
  display: grid;
  gap: var(--spacing-xl);
}

.grid--2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid--3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid--4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Responsive grid */
@media (max-width: 768px) {
  .grid--2,
  .grid--3,
  .grid--4 {
    grid-template-columns: 1fr;
  }
}

/* Flexbox utilities */
.flex {
  display: flex;
}

.flex--center {
  align-items: center;
  justify-content: center;
}

.flex--between {
  justify-content: space-between;
}

.flex--column {
  flex-direction: column;
}

.flex--wrap {
  flex-wrap: wrap;
}

/* Spacing utilities */
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }
.ml-auto { margin-left: auto; }
.mr-auto { margin-right: auto; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* ==========================================================================
   Header Component
   ========================================================================== */

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border-light);
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
}

.header__nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.header__logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header__logo-img {
  height: 40px;
  width: auto;
}

.header__logo-text {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.header__cta {
  /* Button styles already defined in .btn classes */
}

/* ==========================================================================
   Hero Section
   ========================================================================== */

.hero {
  padding: calc(80px + var(--spacing-4xl)) var(--spacing-md) var(--spacing-4xl);
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f4 100%);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="1" fill="%23000" opacity="0.02"/><circle cx="10" cy="90" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.hero__container {
  max-width: 1000px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.hero__header {
  margin-bottom: var(--spacing-3xl);
}

.hero__title {
  font-size: clamp(var(--font-size-3xl), 5vw, var(--font-size-5xl));
  margin-bottom: var(--spacing-lg);
  line-height: var(--line-height-tight);
}

.hero__title-light {
  font-weight: var(--font-weight-light);
  color: var(--color-text-secondary);
}

.hero__title-bold {
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--line-height-relaxed);
}

.hero__categories {
  margin-bottom: var(--spacing-3xl);
  display: flex;
  justify-content: center;
}

.hero__categories-img {
  max-width: 400px;
  height: auto;
}

.hero__cta {
  margin-bottom: var(--spacing-xl);
}

.hero__download-btn {
  /* Button styles already defined */
}

/* ==========================================================================
   Features Section
   ========================================================================== */

.features {
  padding: var(--spacing-5xl) var(--spacing-md);
  background: var(--color-background-primary);
}

.features__container {
  max-width: 1200px;
  margin: 0 auto;
}

.features__header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
}

.features__title {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.features__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
}

/* Feature Card Component */
.feature-card {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border-light);
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card__image-wrapper {
  margin-bottom: var(--spacing-lg);
  position: relative;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  background: var(--color-background-primary);
  padding: var(--spacing-md);
}

.feature-card__image {
  width: 100%;
  height: 200px;
  object-fit: contain;
  border-radius: var(--border-radius-md);
}

.feature-card__content {
  position: relative;
  z-index: 1;
}

.feature-card__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.feature-card__description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* ==========================================================================
   Steps Section
   ========================================================================== */

.steps {
  padding: var(--spacing-5xl) var(--spacing-md);
  background: var(--color-background-secondary);
  position: relative;
}

.steps__container {
  max-width: 1200px;
  margin: 0 auto;
}

.steps__header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
}

.steps__title {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.steps__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.steps__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-4xl);
}

/* Step Component */
.step {
  text-align: center;
  position: relative;
}

.step__icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  position: relative;
}

.step__icon::after {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: var(--gradient-primary);
  z-index: -1;
  opacity: 0.2;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
}

.step__number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
}

.step__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.step__description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.steps__visual {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.steps__visual-img {
  max-width: 600px;
  height: auto;
  margin: 0 auto;
}

.steps__savings {
  text-align: center;
}

.steps__savings-img {
  max-width: 400px;
  height: auto;
  margin: 0 auto;
}

/* ==========================================================================
   CTA Section
   ========================================================================== */

.cta-section {
  padding: var(--spacing-5xl) var(--spacing-md);
  background: var(--color-background-primary);
  text-align: center;
}

.cta-section__container {
  max-width: 800px;
  margin: 0 auto;
}

.cta-section__title {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.cta-section__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-2xl);
  line-height: var(--line-height-relaxed);
}

.cta-section__buttons {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  flex-wrap: wrap;
}

/* ==========================================================================
   Newsletter Section
   ========================================================================== */

.newsletter {
  padding: var(--spacing-5xl) var(--spacing-md);
  background: var(--color-background-secondary);
}

.newsletter__container {
  max-width: 1000px;
  margin: 0 auto;
}

.newsletter__content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.newsletter__icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.newsletter__icon-img {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.newsletter__text {
  max-width: 600px;
}

.newsletter__title {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.newsletter__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: var(--line-height-relaxed);
}

.newsletter__form {
  margin-bottom: var(--spacing-md);
}

.newsletter__form-group {
  display: flex;
  gap: 0;
  background: var(--color-background-primary);
  border: 2px solid var(--color-border-light);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  transition: border-color var(--transition-normal);
}

.newsletter__form-group:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(183, 113, 229, 0.1);
}

.newsletter__label {
  /* Visually hidden but accessible */
}

.newsletter__input {
  flex: 1;
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  background: transparent;
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  outline: none;
}

.newsletter__input::placeholder {
  color: var(--color-text-light);
}

.newsletter__submit {
  border-radius: 0 var(--border-radius-full) var(--border-radius-full) 0;
  margin: 0;
  border: none;
}

.newsletter__error {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-sm);
  min-height: 1.2em;
}

.newsletter__disclaimer {
  color: var(--color-text-light);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* Responsive newsletter layout */
@media (max-width: 768px) {
  .newsletter__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .newsletter__form-group {
    flex-direction: column;
    border-radius: var(--border-radius-lg);
  }

  .newsletter__submit {
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    margin-top: var(--spacing-sm);
  }
}

/* ==========================================================================
   Footer Component
   ========================================================================== */

.footer {
  background: var(--color-background-tertiary);
  padding: var(--spacing-4xl) var(--spacing-md) var(--spacing-xl);
  border-top: 1px solid var(--color-border-light);
}

.footer__container {
  max-width: 1200px;
  margin: 0 auto;
}

.footer__content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer__brand {
  max-width: 300px;
}

.footer__logo {
  margin-bottom: var(--spacing-md);
}

.footer__description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.footer__nav-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer__nav-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.footer__nav-link:hover {
  color: var(--color-primary);
}

.footer__social-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.footer__social-links {
  display: flex;
  gap: var(--spacing-md);
}

.footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-background-primary);
  border: 1px solid var(--color-border-light);
  transition: all var(--transition-normal);
}

.footer__social-link:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
  transform: translateY(-2px);
}

.footer__social-link:hover .footer__social-icon {
  filter: brightness(0) invert(1);
}

.footer__social-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--color-border-light);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer__copyright {
  color: var(--color-text-light);
  font-size: var(--font-size-sm);
}

.footer__legal {
  display: flex;
  gap: var(--spacing-lg);
}

.footer__legal-link {
  color: var(--color-text-light);
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.footer__legal-link:hover {
  color: var(--color-primary);
}

/* ==========================================================================
   Responsive Utilities
   ========================================================================== */

/* Container queries support (progressive enhancement) */
@supports (container-type: inline-size) {
  .feature-card {
    container-type: inline-size;
  }

  @container (max-width: 300px) {
    .feature-card__title {
      font-size: var(--font-size-lg);
    }
  }
}

/* Responsive typography */
.responsive-text {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
}

/* Responsive spacing */
.responsive-padding {
  padding: clamp(1rem, 5vw, 3rem);
}

/* Responsive grid utilities */
.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  gap: var(--spacing-xl);
}

/* Responsive flexbox utilities */
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.flex-responsive > * {
  flex: 1 1 300px;
}

/* Hide/show utilities for different screen sizes */
.hide-mobile {
  display: none;
}

.hide-tablet {
  display: block;
}

.hide-desktop {
  display: block;
}

@media (min-width: 768px) {
  .hide-mobile {
    display: block;
  }

  .hide-tablet {
    display: none;
  }

  .show-tablet {
    display: block;
  }
}

@media (min-width: 1024px) {
  .hide-desktop {
    display: none;
  }

  .show-desktop {
    display: block;
  }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

/* Tablet styles */
@media (max-width: 1024px) {
  .hero {
    padding: calc(80px + var(--spacing-3xl)) var(--spacing-md) var(--spacing-3xl);
  }

  .hero__title {
    font-size: var(--font-size-4xl);
  }

  .features__grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .steps__grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .header__nav {
    padding: var(--spacing-md);
  }

  .header__logo-text {
    font-size: var(--font-size-xl);
  }

  .hero {
    padding: calc(80px + var(--spacing-2xl)) var(--spacing-md) var(--spacing-2xl);
  }

  .hero__title {
    font-size: var(--font-size-3xl);
  }

  .hero__categories-img {
    max-width: 300px;
  }

  .features,
  .steps,
  .cta-section,
  .newsletter {
    padding: var(--spacing-3xl) var(--spacing-md);
  }

  .features__title,
  .steps__title,
  .cta-section__title {
    font-size: var(--font-size-3xl);
  }

  .newsletter__title {
    font-size: var(--font-size-2xl);
  }

  .cta-section__buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-section__buttons .btn {
    width: 100%;
    max-width: 300px;
  }

  .footer__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer__nav-list {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  .footer__bottom {
    flex-direction: column;
    text-align: center;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .hero__title {
    font-size: var(--font-size-2xl);
  }

  .features__title,
  .steps__title,
  .cta-section__title {
    font-size: var(--font-size-2xl);
  }

  .step__icon {
    width: 60px;
    height: 60px;
  }

  .step__number {
    font-size: var(--font-size-xl);
  }

  /* Improve touch targets */
  .btn {
    min-height: 44px;
    padding: var(--spacing-md) var(--spacing-lg);
  }

  /* Reduce spacing on very small screens */
  .hero,
  .features,
  .steps,
  .cta-section,
  .newsletter {
    padding: var(--spacing-2xl) var(--spacing-sm);
  }

  /* Stack newsletter form vertically */
  .newsletter__form-group {
    flex-direction: column;
  }

  .newsletter__input {
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  }

  .newsletter__submit {
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .hero__title {
    font-size: var(--font-size-xl);
  }

  .header__nav {
    padding: var(--spacing-sm);
  }

  .header__logo-text {
    font-size: var(--font-size-lg);
  }

  .btn {
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

/* Landscape orientation on mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .hero {
    padding: calc(80px + var(--spacing-lg)) var(--spacing-md) var(--spacing-lg);
  }

  .hero__title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
  }

  .hero__categories {
    margin-bottom: var(--spacing-lg);
  }
}

/* Print styles */
@media print {
  .header,
  .newsletter,
  .cta-section {
    display: none;
  }

  .hero {
    padding: var(--spacing-xl) 0;
  }

  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
  }
}

/* ==========================================================================
   Browser Compatibility and Fallbacks
   ========================================================================== */

/* Fallback for browsers without CSS Grid support */
@supports not (display: grid) {
  .features__grid,
  .steps__grid {
    display: flex;
    flex-wrap: wrap;
  }

  .feature-card,
  .step {
    flex: 1 1 300px;
    margin: var(--spacing-md);
  }
}

/* Fallback for browsers without CSS custom properties */
.no-css-custom-properties {
  /* Fallback colors */
  --color-primary: #B771E5;
  --color-secondary: #ED9292;
  --color-text-primary: #514D4F;
  --color-text-secondary: #858082;
}

/* Fallback for browsers without backdrop-filter */
@supports not (backdrop-filter: blur(10px)) {
  .header {
    background: rgba(255, 255, 255, 0.98);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #8B5CF6;
    --color-secondary: #EF4444;
    --color-text-primary: #000000;
    --color-text-secondary: #374151;
    --color-border-light: #000000;
  }

  .btn--primary {
    border: 2px solid #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .step__icon::after {
    animation: none;
  }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  .dark-mode-support {
    --color-background-primary: #1F2937;
    --color-background-secondary: #374151;
    --color-text-primary: #F9FAFB;
    --color-text-secondary: #D1D5DB;
    --color-border-light: #4B5563;
  }
}

/* Focus management for better accessibility */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-primary);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error states */
.error {
  border-color: var(--color-error) !important;
  background-color: rgba(239, 68, 68, 0.05);
}

.success {
  border-color: var(--color-success) !important;
  background-color: rgba(16, 185, 129, 0.05);
}

/* Utility classes for JavaScript interactions */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Animation classes for JavaScript */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-up.animate {
  opacity: 1;
  transform: translateY(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.scale-in.animate {
  opacity: 1;
  transform: scale(1);
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Remove will-change after animation */
.animation-complete {
  will-change: auto;
}
