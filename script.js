document.addEventListener('DOMContentLoaded', () => {
  const form = document.getElementById('lead-form');

  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    const email = form.email.value;

    try {
      await fetch('https://script.google.com/macros/s/AKfycbyGm_QB56tMXz5-RR8w-3AkC4OVECpMzOw5sI6SzrHgI06tOx5yxnjL0IuxiIygyZBa/exec', {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain;charset=utf-8'
        },
        body: JSON.stringify({ email })
      });

      alert("Obrigado! Você receberá novidades em breve 😊");
      form.reset();
    } catch (error) {
      alert("Ops! Algo deu errado. Tente novamente.");
      console.error("Erro ao enviar lead:", error);
    }
  });
});
