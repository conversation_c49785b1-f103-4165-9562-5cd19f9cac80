/**
 * Izy Mercado Landing Page v2.0 - JavaScript
 * Modern ES6+ implementation with enhanced functionality
 */

class IzyLandingPage {
  constructor() {
    this.form = null;
    this.emailInput = null;
    this.errorElement = null;
    this.submitButton = null;
    this.isSubmitting = false;

    // Configuration
    this.config = {
      apiEndpoint: 'https://script.google.com/macros/s/AKfycbyGm_QB56tMXz5-RR8w-3AkC4OVECpMzOw5sI6SzrHgI06tOx5yxnjL0IuxiIygyZBa/exec',
      timeout: 10000, // 10 seconds
      retryAttempts: 3,
      retryDelay: 1000 // 1 second
    };

    this.init();
  }

  /**
   * Initialize the landing page functionality
   */
  init() {
    this.bindElements();
    this.attachEventListeners();
    this.initializeAnimations();
    this.setupAccessibility();
    this.setupImageOptimization();
    this.setupResponsiveBehavior();
    this.setupPerformanceOptimizations();
  }

  /**
   * Bind DOM elements
   */
  bindElements() {
    this.form = document.getElementById('lead-form');
    this.emailInput = document.getElementById('email');
    this.errorElement = document.getElementById('email-error');
    this.submitButton = this.form?.querySelector('button[type="submit"]');

    if (!this.form || !this.emailInput || !this.errorElement || !this.submitButton) {
      console.warn('Some required elements not found. Newsletter functionality may be limited.');
    }
  }

  /**
   * Attach event listeners
   */
  attachEventListeners() {
    // Newsletter form submission
    if (this.form) {
      this.form.addEventListener('submit', this.handleFormSubmit.bind(this));
    }

    // Email input validation
    if (this.emailInput) {
      this.emailInput.addEventListener('input', this.handleEmailInput.bind(this));
      this.emailInput.addEventListener('blur', this.validateEmail.bind(this));
    }

    // Smooth scrolling for anchor links
    document.addEventListener('click', this.handleSmoothScroll.bind(this));

    // Header scroll effect
    window.addEventListener('scroll', this.throttle(this.handleHeaderScroll.bind(this), 16));

    // Intersection Observer for animations
    this.setupIntersectionObserver();
  }

  /**
   * Handle form submission
   */
  async handleFormSubmit(event) {
    event.preventDefault();

    if (this.isSubmitting) return;

    const email = this.emailInput.value.trim();

    // Validate email
    if (!this.isValidEmail(email)) {
      this.showError('Por favor, insira um endereço de e-mail válido.');
      this.emailInput.focus();
      return;
    }

    this.clearError();
    this.setSubmittingState(true);

    try {
      await this.submitEmailWithRetry(email);
      this.showSuccess();
      this.form.reset();
    } catch (error) {
      console.error('Error submitting email:', error);
      this.showError('Ops! Algo deu errado. Tente novamente em alguns instantes.');
    } finally {
      this.setSubmittingState(false);
    }
  }

  /**
   * Submit email with retry logic
   */
  async submitEmailWithRetry(email, attempt = 1) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain;charset=utf-8'
        },
        body: JSON.stringify({ email, timestamp: Date.now() }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response;
    } catch (error) {
      if (attempt < this.config.retryAttempts) {
        await this.delay(this.config.retryDelay * attempt);
        return this.submitEmailWithRetry(email, attempt + 1);
      }
      throw error;
    }
  }

  /**
   * Handle email input changes
   */
  handleEmailInput(event) {
    const email = event.target.value.trim();

    // Clear error when user starts typing
    if (this.errorElement.textContent) {
      this.clearError();
    }

    // Real-time validation feedback
    if (email && !this.isValidEmail(email)) {
      event.target.setAttribute('aria-invalid', 'true');
    } else {
      event.target.removeAttribute('aria-invalid');
    }
  }

  /**
   * Validate email on blur
   */
  validateEmail() {
    const email = this.emailInput.value.trim();

    if (email && !this.isValidEmail(email)) {
      this.showError('Por favor, insira um endereço de e-mail válido.');
      return false;
    }

    this.clearError();
    return true;
  }

  /**
   * Check if email is valid
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Show error message
   */
  showError(message) {
    if (this.errorElement) {
      this.errorElement.textContent = message;
      this.errorElement.setAttribute('aria-live', 'polite');
      this.emailInput?.setAttribute('aria-invalid', 'true');
    }
  }

  /**
   * Clear error message
   */
  clearError() {
    if (this.errorElement) {
      this.errorElement.textContent = '';
      this.emailInput?.removeAttribute('aria-invalid');
    }
  }

  /**
   * Show success message
   */
  showSuccess() {
    // Create a temporary success message
    const successMessage = document.createElement('div');
    successMessage.className = 'newsletter__success';
    successMessage.textContent = 'Obrigado! Você receberá novidades em breve 😊';
    successMessage.setAttribute('role', 'alert');
    successMessage.setAttribute('aria-live', 'polite');

    // Insert after the form
    this.form.parentNode.insertBefore(successMessage, this.form.nextSibling);

    // Remove after 5 seconds
    setTimeout(() => {
      if (successMessage.parentNode) {
        successMessage.parentNode.removeChild(successMessage);
      }
    }, 5000);
  }

  /**
   * Set submitting state
   */
  setSubmittingState(isSubmitting) {
    this.isSubmitting = isSubmitting;

    if (this.submitButton) {
      this.submitButton.disabled = isSubmitting;
      this.submitButton.textContent = isSubmitting ? 'Enviando...' : 'Receber novidades';
      this.submitButton.setAttribute('aria-busy', isSubmitting.toString());
    }

    if (this.emailInput) {
      this.emailInput.disabled = isSubmitting;
    }
  }

  /**
   * Handle smooth scrolling for anchor links
   */
  handleSmoothScroll(event) {
    const link = event.target.closest('a[href^="#"]');
    if (!link) return;

    const href = link.getAttribute('href');
    if (href === '#') return;

    const target = document.querySelector(href);
    if (!target) return;

    event.preventDefault();

    const headerHeight = document.querySelector('.header')?.offsetHeight || 0;
    const targetPosition = target.offsetTop - headerHeight - 20;

    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
  }

  /**
   * Handle header scroll effect
   */
  handleHeaderScroll() {
    const header = document.querySelector('.header');
    if (!header) return;

    const scrollY = window.scrollY;
    const threshold = 100;

    if (scrollY > threshold) {
      header.classList.add('header--scrolled');
    } else {
      header.classList.remove('header--scrolled');
    }
  }

  /**
   * Initialize animations
   */
  initializeAnimations() {
    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
      .newsletter__success {
        background: var(--color-success);
        color: white;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--border-radius-md);
        margin-top: var(--spacing-md);
        text-align: center;
        animation: slideInUp 0.3s ease-out;
      }

      .header--scrolled {
        background: rgba(255, 255, 255, 0.98);
        box-shadow: var(--shadow-md);
      }

      .fade-in {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.6s ease-out, transform 0.6s ease-out;
      }

      .fade-in.visible {
        opacity: 1;
        transform: translateY(0);
      }

      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Setup intersection observer for scroll animations
   */
  setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) return;

    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.feature-card, .step, .cta-section, .newsletter');
    animatedElements.forEach(el => {
      el.classList.add('fade-in');
      observer.observe(el);
    });
  }

  /**
   * Setup accessibility enhancements
   */
  setupAccessibility() {
    // Add skip links functionality
    const skipLink = document.querySelector('.skip-link');
    if (skipLink) {
      skipLink.addEventListener('click', (e) => {
        e.preventDefault();
        const target = document.querySelector(skipLink.getAttribute('href'));
        if (target) {
          target.focus();
          target.scrollIntoView({ behavior: 'smooth' });
        }
      });
    }

    // Enhance button accessibility
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
      if (!button.getAttribute('aria-label') && !button.textContent.trim()) {
        console.warn('Button without accessible label found:', button);
      }
    });

    // Add keyboard navigation for custom elements
    this.setupKeyboardNavigation();
  }

  /**
   * Setup keyboard navigation
   */
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Handle Enter key on buttons that might not be proper buttons
      if (e.key === 'Enter') {
        const target = e.target;
        if (target.getAttribute('role') === 'button' && !target.disabled) {
          target.click();
        }
      }

      // Handle Escape key to close any open modals or dropdowns
      if (e.key === 'Escape') {
        const activeElement = document.activeElement;
        if (activeElement && activeElement.blur) {
          activeElement.blur();
        }
      }
    });
  }

  /**
   * Throttle function for performance
   */
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Delay utility function
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Setup image optimization
   */
  setupImageOptimization() {
    // Check for native lazy loading support
    if (!('loading' in HTMLImageElement.prototype)) {
      document.documentElement.classList.add('no-loading-lazy');
      this.setupLazyLoadingFallback();
    }

    // Setup image loading states
    this.setupImageLoadingStates();

    // Preload critical images
    this.preloadCriticalImages();
  }

  /**
   * Setup lazy loading fallback for older browsers
   */
  setupLazyLoadingFallback() {
    if (!('IntersectionObserver' in window)) return;

    const lazyImages = document.querySelectorAll('img[loading="lazy"]');

    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src || img.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    }, {
      rootMargin: '50px 0px'
    });

    lazyImages.forEach(img => {
      imageObserver.observe(img);
    });
  }

  /**
   * Setup image loading states
   */
  setupImageLoadingStates() {
    const images = document.querySelectorAll('img[loading="lazy"]');

    images.forEach(img => {
      if (img.complete) {
        img.classList.add('loaded');
      } else {
        img.addEventListener('load', () => {
          img.classList.add('loaded');
        });

        img.addEventListener('error', () => {
          console.warn('Failed to load image:', img.src);
          img.classList.add('error');
        });
      }
    });
  }

  /**
   * Preload critical images
   */
  preloadCriticalImages() {
    const criticalImages = [
      './LP Izy/logo-izy1.png',
      './content-categorias.svg'
    ];

    criticalImages.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });
  }

  /**
   * Setup responsive behavior
   */
  setupResponsiveBehavior() {
    // Handle viewport changes
    this.handleViewportChanges();

    // Setup responsive navigation
    this.setupResponsiveNavigation();

    // Handle orientation changes
    this.handleOrientationChanges();

    // Setup responsive images
    this.setupResponsiveImages();
  }

  /**
   * Handle viewport changes
   */
  handleViewportChanges() {
    let resizeTimer;

    window.addEventListener('resize', () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(() => {
        this.updateViewportClasses();
        this.recalculateLayout();
      }, 250);
    });

    // Initial setup
    this.updateViewportClasses();
  }

  /**
   * Update viewport classes based on screen size
   */
  updateViewportClasses() {
    const body = document.body;
    const width = window.innerWidth;

    // Remove existing classes
    body.classList.remove('viewport-mobile', 'viewport-tablet', 'viewport-desktop');

    // Add appropriate class
    if (width < 768) {
      body.classList.add('viewport-mobile');
    } else if (width < 1024) {
      body.classList.add('viewport-tablet');
    } else {
      body.classList.add('viewport-desktop');
    }
  }

  /**
   * Setup responsive navigation
   */
  setupResponsiveNavigation() {
    // Add mobile menu functionality if needed
    const header = document.querySelector('.header');
    if (!header) return;

    // Handle mobile menu toggle (if implemented)
    const mobileMenuToggle = header.querySelector('.mobile-menu-toggle');
    if (mobileMenuToggle) {
      mobileMenuToggle.addEventListener('click', this.toggleMobileMenu.bind(this));
    }
  }

  /**
   * Toggle mobile menu
   */
  toggleMobileMenu() {
    const header = document.querySelector('.header');
    const nav = header?.querySelector('.header__nav');

    if (nav) {
      nav.classList.toggle('nav-open');

      // Update ARIA attributes
      const toggle = header.querySelector('.mobile-menu-toggle');
      if (toggle) {
        const isOpen = nav.classList.contains('nav-open');
        toggle.setAttribute('aria-expanded', isOpen.toString());
      }
    }
  }

  /**
   * Handle orientation changes
   */
  handleOrientationChanges() {
    window.addEventListener('orientationchange', () => {
      // Small delay to ensure viewport dimensions are updated
      setTimeout(() => {
        this.updateViewportClasses();
        this.recalculateLayout();
      }, 100);
    });
  }

  /**
   * Setup responsive images
   */
  setupResponsiveImages() {
    // Handle high DPI displays
    if (window.devicePixelRatio > 1) {
      document.documentElement.classList.add('high-dpi');
    }

    // Setup responsive image switching based on viewport
    this.updateResponsiveImages();

    window.addEventListener('resize', this.throttle(() => {
      this.updateResponsiveImages();
    }, 250));
  }

  /**
   * Update responsive images based on viewport
   */
  updateResponsiveImages() {
    const images = document.querySelectorAll('img[data-responsive]');
    const viewportWidth = window.innerWidth;

    images.forEach(img => {
      const sizes = img.dataset.responsive.split(',');
      let selectedSrc = img.src;

      sizes.forEach(size => {
        const [breakpoint, src] = size.split(':');
        if (viewportWidth >= parseInt(breakpoint)) {
          selectedSrc = src.trim();
        }
      });

      if (img.src !== selectedSrc) {
        img.src = selectedSrc;
      }
    });
  }

  /**
   * Recalculate layout after viewport changes
   */
  recalculateLayout() {
    // Trigger reflow for elements that might need it
    const elementsToRecalculate = document.querySelectorAll('.feature-card, .step');

    elementsToRecalculate.forEach(element => {
      // Force reflow
      element.offsetHeight;
    });

    // Update any dynamic heights or positions
    this.updateDynamicElements();
  }

  /**
   * Update dynamic elements
   */
  updateDynamicElements() {
    // Update header height for fixed positioning calculations
    const header = document.querySelector('.header');
    if (header) {
      document.documentElement.style.setProperty('--header-height', `${header.offsetHeight}px`);
    }

    // Update any other dynamic measurements
    this.updateScrollIndicators();
  }

  /**
   * Update scroll indicators
   */
  updateScrollIndicators() {
    const scrollProgress = window.scrollY / (document.documentElement.scrollHeight - window.innerHeight);
    document.documentElement.style.setProperty('--scroll-progress', scrollProgress.toString());
  }

  /**
   * Setup performance optimizations
   */
  setupPerformanceOptimizations() {
    // Defer non-critical JavaScript
    this.deferNonCriticalJS();

    // Setup resource hints
    this.setupResourceHints();

    // Monitor performance
    this.monitorPerformance();

    // Setup service worker for caching
    this.setupServiceWorker();

    // Optimize font loading
    this.optimizeFontLoading();
  }

  /**
   * Defer non-critical JavaScript
   */
  deferNonCriticalJS() {
    // Load non-critical scripts after page load
    window.addEventListener('load', () => {
      // Example: Load analytics or other non-critical scripts
      this.loadNonCriticalScripts();
    });
  }

  /**
   * Load non-critical scripts
   */
  loadNonCriticalScripts() {
    // This would load analytics, chat widgets, etc.
    // For now, we'll just log that we're ready for them
    console.log('Ready to load non-critical scripts');
  }

  /**
   * Setup resource hints
   */
  setupResourceHints() {
    // Add prefetch hints for likely next pages
    const prefetchUrls = [
      '/about',
      '/contact',
      '/app-download'
    ];

    prefetchUrls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    });
  }

  /**
   * Monitor performance
   */
  monitorPerformance() {
    // Use Performance Observer API if available
    if ('PerformanceObserver' in window) {
      this.setupPerformanceObserver();
    }

    // Monitor Core Web Vitals
    this.monitorCoreWebVitals();

    // Track custom metrics
    this.trackCustomMetrics();
  }

  /**
   * Setup Performance Observer
   */
  setupPerformanceObserver() {
    try {
      // Observe paint metrics
      const paintObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.log(`${entry.name}: ${entry.startTime}ms`);
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });

      // Observe navigation metrics
      const navObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.log('Navigation timing:', {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart
          });
        });
      });
      navObserver.observe({ entryTypes: ['navigation'] });

    } catch (error) {
      console.warn('Performance Observer not supported:', error);
    }
  }

  /**
   * Monitor Core Web Vitals
   */
  monitorCoreWebVitals() {
    // Largest Contentful Paint (LCP)
    this.measureLCP();

    // First Input Delay (FID)
    this.measureFID();

    // Cumulative Layout Shift (CLS)
    this.measureCLS();
  }

  /**
   * Measure Largest Contentful Paint
   */
  measureLCP() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log('LCP:', lastEntry.startTime);
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (error) {
        console.warn('LCP measurement not supported:', error);
      }
    }
  }

  /**
   * Measure First Input Delay
   */
  measureFID() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            console.log('FID:', entry.processingStart - entry.startTime);
          });
        });
        observer.observe({ entryTypes: ['first-input'] });
      } catch (error) {
        console.warn('FID measurement not supported:', error);
      }
    }
  }

  /**
   * Measure Cumulative Layout Shift
   */
  measureCLS() {
    if ('PerformanceObserver' in window) {
      try {
        let clsValue = 0;
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
              console.log('CLS:', clsValue);
            }
          });
        });
        observer.observe({ entryTypes: ['layout-shift'] });
      } catch (error) {
        console.warn('CLS measurement not supported:', error);
      }
    }
  }

  /**
   * Track custom metrics
   */
  trackCustomMetrics() {
    // Track time to interactive
    this.trackTimeToInteractive();

    // Track custom business metrics
    this.trackBusinessMetrics();
  }

  /**
   * Track time to interactive
   */
  trackTimeToInteractive() {
    const startTime = performance.now();

    // Consider page interactive when all critical elements are loaded
    const checkInteractive = () => {
      const criticalElements = document.querySelectorAll('.hero, .features, .btn');
      const allLoaded = Array.from(criticalElements).every(el => el.offsetHeight > 0);

      if (allLoaded) {
        const timeToInteractive = performance.now() - startTime;
        console.log('Time to Interactive:', timeToInteractive);
      } else {
        requestAnimationFrame(checkInteractive);
      }
    };

    requestAnimationFrame(checkInteractive);
  }

  /**
   * Track business metrics
   */
  trackBusinessMetrics() {
    // Track newsletter signup attempts
    if (this.form) {
      this.form.addEventListener('submit', () => {
        console.log('Newsletter signup attempted');
      });
    }

    // Track CTA button clicks
    const ctaButtons = document.querySelectorAll('.btn--primary');
    ctaButtons.forEach(button => {
      button.addEventListener('click', () => {
        console.log('CTA button clicked:', button.textContent);
      });
    });
  }

  /**
   * Setup service worker
   */
  setupServiceWorker() {
    if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('SW registered:', registration);
          })
          .catch(error => {
            console.log('SW registration failed:', error);
          });
      });
    }
  }

  /**
   * Optimize font loading
   */
  optimizeFontLoading() {
    // Use font-display: swap for better performance
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        console.log('Fonts loaded');
        document.body.classList.add('fonts-loaded');
      });
    }

    // Preload critical font variants
    const fontPreloads = [
      'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2',
      'https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmEU9fBBc4.woff2'
    ];

    fontPreloads.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      link.href = url;
      document.head.appendChild(link);
    });
  }

  /**
   * Debug mode for development
   */
  enableDebugMode() {
    console.log('Izy Landing Page Debug Mode Enabled');

    // Add visual indicators for accessibility
    const style = document.createElement('style');
    style.textContent = `
      [aria-invalid="true"] {
        outline: 2px solid var(--color-error) !important;
      }

      [aria-hidden="true"] {
        opacity: 0.5 !important;
      }

      .visually-hidden {
        background: yellow !important;
        color: black !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
      }
    `;
    document.head.appendChild(style);

    // Log performance metrics
    if ('performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const perfData = performance.getEntriesByType('navigation')[0];
          console.log('Page Load Performance:', {
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
            totalTime: perfData.loadEventEnd - perfData.fetchStart
          });
        }, 0);
      });
    }
  }
}

// Feature detection and polyfills
(function() {
  'use strict';

  // Check for CSS custom properties support
  if (!window.CSS || !CSS.supports('color', 'var(--test)')) {
    document.documentElement.classList.add('no-css-custom-properties');
  }

  // Check for IntersectionObserver support
  if (!('IntersectionObserver' in window)) {
    // Load polyfill if needed
    console.warn('IntersectionObserver not supported. Consider loading a polyfill.');
  }

  // Check for fetch support
  if (!('fetch' in window)) {
    console.warn('Fetch API not supported. Consider loading a polyfill.');
  }

  // Add focus-visible polyfill class
  if (!('focus-visible' in document.documentElement.style)) {
    document.documentElement.classList.add('js-focus-visible');
  }
})();

// Global error handling
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);

  // Track errors for monitoring
  if (window.gtag) {
    gtag('event', 'exception', {
      description: event.error.message,
      fatal: false
    });
  }
});

// Unhandled promise rejection handling
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);

  // Prevent the default browser behavior
  event.preventDefault();

  // Track promise rejections
  if (window.gtag) {
    gtag('event', 'exception', {
      description: 'Unhandled promise rejection: ' + event.reason,
      fatal: false
    });
  }
});

// Initialize the landing page when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  try {
    const landingPage = new IzyLandingPage();

    // Enable debug mode in development
    if (window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.search.includes('debug=true')) {
      landingPage.enableDebugMode();
    }

    // Make instance available globally for debugging
    window.izyLandingPage = landingPage;

    // Dispatch custom event for other scripts
    window.dispatchEvent(new CustomEvent('izyLandingPageReady', {
      detail: { instance: landingPage }
    }));

  } catch (error) {
    console.error('Failed to initialize landing page:', error);

    // Fallback functionality
    initializeFallbackFunctionality();
  }
});

// Fallback functionality for when main script fails
function initializeFallbackFunctionality() {
  console.log('Initializing fallback functionality...');

  // Basic form handling
  const form = document.getElementById('lead-form');
  if (form) {
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      alert('Obrigado pelo seu interesse! Tente novamente em alguns instantes.');
    });
  }

  // Basic smooth scrolling
  document.addEventListener('click', (e) => {
    const link = e.target.closest('a[href^="#"]');
    if (link) {
      e.preventDefault();
      const target = document.querySelector(link.getAttribute('href'));
      if (target) {
        target.scrollIntoView({ behavior: 'smooth' });
      }
    }
  });
}

// Handle page visibility changes for performance
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    // Page is hidden, pause any animations or timers
    console.log('Page hidden - pausing non-essential operations');

    // Pause any running animations
    document.querySelectorAll('.step__icon::after').forEach(el => {
      el.style.animationPlayState = 'paused';
    });
  } else {
    // Page is visible again
    console.log('Page visible - resuming operations');

    // Resume animations
    document.querySelectorAll('.step__icon::after').forEach(el => {
      el.style.animationPlayState = 'running';
    });
  }
});

// Service Worker registration for PWA capabilities
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    // Only register on HTTPS or localhost
    if (window.location.protocol === 'https:' ||
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1') {

      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          console.log('SW registered: ', registration);

          // Check for updates
          registration.addEventListener('updatefound', () => {
            console.log('New service worker version available');
          });
        })
        .catch(registrationError => {
          console.log('SW registration failed: ', registrationError);
        });
    }
  });
}

// Handle online/offline status
window.addEventListener('online', () => {
  console.log('Connection restored');
  document.body.classList.remove('offline');

  // Retry any failed requests
  if (window.izyLandingPage && window.izyLandingPage.retryFailedRequests) {
    window.izyLandingPage.retryFailedRequests();
  }
});

window.addEventListener('offline', () => {
  console.log('Connection lost');
  document.body.classList.add('offline');
});

// Performance monitoring
if ('PerformanceObserver' in window) {
  // Monitor long tasks
  try {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 50) {
          console.warn('Long task detected:', entry.duration + 'ms');
        }
      });
    });
    observer.observe({ entryTypes: ['longtask'] });
  } catch (error) {
    console.warn('Long task monitoring not supported:', error);
  }
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  // Clean up any resources
  if (window.izyLandingPage && window.izyLandingPage.cleanup) {
    window.izyLandingPage.cleanup();
  }
});
