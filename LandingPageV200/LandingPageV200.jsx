import React from "react";
import QUERGanharR50 from "./QUER-GANHAR-r-50.svg";
import actions from "./actions.svg";
import addTabButton from "./add-tab-button.svg";
import apple from "./apple.svg";
import arrowIcon from "./arrow-icon.svg";
import arrow from "./arrow.svg";
import basket21 from "./basket-2.png";
import basket2 from "./basket-2.svg";
import basket31 from "./basket-3.png";
import basket3 from "./basket-3.svg";
import basket4 from "./basket-4.png";
import basket from "./basket.png";
import basket1 from "./basket.svg";
import chatgptImage10DeJulDe202511460512 from "./chatgpt-image-10-de-jul-de-2025-11-46-05-1-2.png";
import chatgptImage10DeJulDe20251146051 from "./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png";
import chatgptImage10DeJulDe20251146052 from "./chatgpt-image-10-de-jul-de-2025-11-46-05-2.png";
import chatgptImage24DeMaiDe20251347411 from "./chatgpt-image-24-de-mai-de-2025-13-47-41-1.png";
import dollarFrontColor from "./dollar-front-color.png";
import economizeEGanheCashback from "./economize-e-ganhe-cashback.svg";
import favicon from "./favicon.png";
import googlePlay from "./google-play.svg";
import heartFavorite from "./heart-favorite.svg";
import image from "./image.png";
import image1 from "./image.svg";
import imagem1 from "./imagem-1.png";
import lock from "./lock.svg";
import moreIcon from "./more-icon.svg";
import notification2 from "./notification-2.png";
import notification21 from "./notification-2.svg";
import notification3 from "./notification-3.png";
import notification4 from "./notification-4.png";
import notification5 from "./notification-5.png";
import notification1 from "./notification.png";
import notification from "./notification.svg";
import path2 from "./path-2.svg";
import path3 from "./path-3.svg";
import path4 from "./path-4.svg";
import path5 from "./path-5.svg";
import path6 from "./path-6.svg";
import path7 from "./path-7.svg";
import path8 from "./path-8.svg";
import path9 from "./path-9.svg";
import path10 from "./path-10.svg";
import path11 from "./path-11.svg";
import path12 from "./path-12.svg";
import path13 from "./path-13.svg";
import path14 from "./path-14.svg";
import path15 from "./path-15.svg";
import path16 from "./path-16.svg";
import path17 from "./path-17.svg";
import path from "./path.svg";
import product1 from "./product-1.png";
import product2 from "./product-2.png";
import product from "./product.png";
import rectangle from "./rectangle.svg";
import showSideMenuButton from "./show-side-menu-button.svg";
import smallArrowDown from "./small-arrow-down.svg";
import social from "./social.svg";
import starsLightSparkle12 from "./stars-light-sparkle-1-2.svg";
import starsLightSparkle13 from "./stars-light-sparkle-1-3.svg";
import starsLightSparkle14 from "./stars-light-sparkle-1-4.svg";
import starsLightSparkle1 from "./stars-light-sparkle-1.svg";
import "./style.css";
import verticalDivider from "./vertical-divider.svg";
import websiteFavicon2 from "./website-favicon-2.png";
import websiteFavicon3 from "./website-favicon-3.png";
import websiteFavicon4 from "./website-favicon-4.png";
import websiteFavicon5 from "./website-favicon-5.png";
import websiteFavicon6 from "./website-favicon-6.png";
import websiteFavicon7 from "./website-favicon-7.png";
import websiteFavicon8 from "./website-favicon-8.png";
import websiteFavicon9 from "./website-favicon-9.png";
import websiteFavicon10 from "./website-favicon-10.png";
import websiteFavicon from "./website-favicon.png";

export const LandingPage = () => {
  return (
    <div className="landing-page">
      <div className="landing-page-v">
        <div className="footer">
          <div className="div">
            <div className="div-2">
              <div className="div-3">
                <img
                  className="shopping-ecommerce"
                  alt="Shopping ecommerce"
                  src={basket3}
                />

                <div className="text-wrapper">izy</div>
              </div>

              <p className="p">
                O futuro das compras é local, digital e inteligente. Venha com a
                gente!
              </p>
            </div>

            <img className="img" alt="Social" src={social} />
          </div>

          <div className="div">
            <div className="content">
              <div className="content-right">
                <button className="button">
                  <div className="text-wrapper-2">Seja um parceiro</div>
                </button>

                <button className="button">
                  <div className="text-wrapper-2">Termos de uso</div>
                </button>

                <button className="button">
                  <div className="text-wrapper-2">Privacidade</div>
                </button>
              </div>
            </div>

            <div className="store">
              <div className="card-status-pedido">
                <img className="img-2" alt="Social media apple" src={apple} />

                <div className="div-4">
                  <div className="text-wrapper-3">Disponível na</div>

                  <div className="text-wrapper-4">Apple Store</div>
                </div>
              </div>

              <div className="card-status-pedido">
                <img
                  className="img-2"
                  alt="Social media google"
                  src={googlePlay}
                />

                <div className="div-4">
                  <div className="text-wrapper-3">Disponível na</div>

                  <div className="text-wrapper-4">Google Play</div>
                </div>
              </div>
            </div>
          </div>

          <div className="text-content">
            <p className="text-wrapper-5">
              Av. Engenheiro Roberto Freire, 1962, Swaway Shopping, Loja 13,
              Capim Macio, Natal/RN, 59082-095
            </p>

            <div className="company">
              <p className="text-wrapper-5">
                © 2025, Izy Mercado. Todos os direitos reservado
              </p>

              <div className="pointer" />

              <div className="text-wrapper-5">61.134.691/0001-00</div>
            </div>
          </div>
        </div>

        <div className="content-categorias">
          <img className="notification" alt="Notification" src={notification} />

          <div className="div-wrapper">
            <div className="img-3">
              <div className="group">
                <div className="overlap-group">
                  <div className="oval" />

                  <img className="path" alt="Path" src={path8} />

                  <img className="path-2" alt="Path" src={path2} />

                  <img className="path-3" alt="Path" src={path} />

                  <img className="path-4" alt="Path" src={path12} />
                </div>
              </div>
            </div>
          </div>

          <div className="div-wrapper">
            <div className="img-3">
              <div className="group">
                <div className="overlap-group-2">
                  <img className="path-5" alt="Path" src={path6} />

                  <img className="path-6" alt="Path" src={path5} />

                  <img className="path-7" alt="Path" src={path17} />

                  <img className="path-8" alt="Path" src={path11} />
                </div>
              </div>
            </div>
          </div>

          <img
            className="notification-2"
            alt="Notification"
            src={notification21}
          />
        </div>

        <p className="seu-mercado-f-cil-e">
          <span className="span">Seu mercado,</span>

          <span className="text-wrapper-6"> fácil e inteligente</span>
        </p>

        <div className="button-2">
          <p className="text-wrapper-7">Quero fazer minhas compras com a Izy</p>
        </div>

        <div className="overlap">
          <img
            className="chatgpt-image-de"
            alt="Chatgpt image de"
            src={chatgptImage10DeJulDe20251146051}
          />

          <div className="card-status-pedido-2">
            <img className="img-4" alt="Shopping ecommerce" src={basket} />

            <div className="content-2">
              <p className="text-wrapper-8">Sua lista de compras em minutos!</p>

              <p className="text-wrapper-9">
                Em até 5min, você cria sua lista, compara os preços e recebe em
                casa. Tudo num só app
              </p>
            </div>
          </div>

          <div className="card-status-pedido-3">
            <img
              className="img-4"
              alt="Dollar front color"
              src={dollarFrontColor}
            />

            <div className="content-2">
              <div className="text-wrapper-8">Receba dinheiro de volta</div>

              <p className="text-wrapper-9">
                Na Izy, você ganha comodidade e ainda ganha cashback
              </p>
            </div>
          </div>

          <button className="button-3">
            <div className="text-wrapper-10">Compras do mês</div>

            <img
              className="img-2"
              alt="Interface essential"
              src={starsLightSparkle14}
            />
          </button>
        </div>

        <div className="content-3">
          <div className="card">
            <div className="title-wrapper">
              <div className="title">
                <img
                  className="img-3"
                  alt="Interface essential"
                  src={starsLightSparkle12}
                />

                <div className="text-wrapper-11">Crie sua lista</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="overlap-2">
              <div className="title-2">
                <img className="img-3" alt="Shopping ecommerce" src={basket4} />

                <div className="text-wrapper-11">Cote os melhores preços</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="overlap-3">
              <div className="title-3">
                <img
                  className="img-3"
                  alt="Dollar front color"
                  src={dollarFrontColor}
                />

                <img
                  className="img"
                  alt="Economize e ganhe"
                  src={economizeEGanheCashback}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="store-2">
          <div className="card-status-pedido">
            <img className="img-2" alt="Social media apple" src={apple} />

            <div className="div-4">
              <div className="text-wrapper-3">Disponível na</div>

              <div className="text-wrapper-4">Apple Store</div>
            </div>
          </div>

          <div className="card-status-pedido">
            <img className="img-2" alt="Social media google" src={googlePlay} />

            <div className="div-4">
              <div className="text-wrapper-3">Disponível na</div>

              <div className="text-wrapper-4">Google Play</div>
            </div>
          </div>
        </div>

        <div className="content-4">
          <div className="list-types">
            <div className="notification-3">
              <div className="img-2">
                <div className="overlap-group-wrapper">
                  <div className="overlap-group-3">
                    <img className="path-9" alt="Path" src={path4} />

                    <img className="path-10" alt="Path" src={path9} />

                    <img className="path-11" alt="Path" src={path13} />

                    <img className="path-12" alt="Path" src={path3} />

                    <img className="path-13" alt="Path" src={path15} />

                    <img className="path-14" alt="Path" src={path16} />

                    <img className="path-15" alt="Path" src={path16} />

                    <img className="path-16" alt="Path" src={path16} />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <p className="seu-mercado-para">
            <span className="span">Seu mercado,</span>

            <span className="text-wrapper-6"> para todos os momentos!</span>
          </p>
        </div>

        <div className="content-5">
          <img className="img-4" alt="Shopping ecommerce" src={basket31} />

          <p className="text-wrapper-12">
            Receba seus produtos fresquinhos, baixe nosso app!
          </p>
        </div>

        <div className="overlap-4">
          <img
            className="chatgpt-image-de-2"
            alt="Chatgpt image de"
            src={chatgptImage10DeJulDe202511460512}
          />

          <div className="card-status-pedido-4">
            <img className="img-4" alt="Shopping ecommerce" src={basket21} />

            <div className="content-2">
              <p className="text-wrapper-8">
                Mais visibilidade para o seu negócio
              </p>

              <p className="text-wrapper-9">
                Seja parceiro da Izy e comece a vender mais, com nossa
                tecnologia
              </p>
            </div>
          </div>

          <div className="card-status-pedido-5">
            <img
              className="img-4"
              alt="Interface essential"
              src={starsLightSparkle13}
            />

            <div className="content-2">
              <p className="text-wrapper-8">
                Relatórios e dados de performance
              </p>

              <p className="text-wrapper-9">
                Com a Izy, você sabe exatamente o que vender e por quanto vai
                vender
              </p>
            </div>
          </div>

          <div className="card-status-pedido-6">
            <div className="button-4">
              <div className="div">
                <div className="div-2">
                  <div className="product">
                    <img className="product-2" alt="Product" src={product} />
                  </div>

                  <div className="text-wrapper-13">Supermercado Konoha</div>
                </div>

                <div className="tag">
                  <img
                    className="img-5"
                    alt="Interface essential"
                    src={starsLightSparkle1}
                  />

                  <div className="text-wrapper-14">Mais completo</div>
                </div>
              </div>

              <div className="div">
                <div className="content-categorias-2">
                  <div className="product-wrapper">
                    <img className="product-3" alt="Product" src={product1} />
                  </div>

                  <div className="imagem-wrapper">
                    <img className="imagem" alt="Imagem" src={imagem1} />
                  </div>

                  <div className="notification-4">
                    <div className="text-wrapper-15">28</div>
                  </div>
                </div>

                <div className="div-4">
                  <div className="text-wrapper-16">Total do pedido</div>

                  <div className="div-3">
                    <div className="text-wrapper-17">R$ 102,00</div>

                    <img className="img-6" alt="Arrows diagrams" src={arrow} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="overlap-5">
          <img
            className="chatgpt-image-de-3"
            alt="Chatgpt image de"
            src={chatgptImage10DeJulDe20251146052}
          />

          <div className="content-text">
            <img
              className="img-3"
              alt="Chatgpt image de"
              src={chatgptImage24DeMaiDe20251347411}
            />

            <div className="content-2">
              <p className="text-wrapper-18">
                Você ganhou R$ 12,00 de cashback!
              </p>

              <p className="voc-vai-receber-um">
                <span className="text-wrapper-19">
                  Você vai receber um PIX na conta onde você pagou o seu pedido
                </span>
              </p>
            </div>
          </div>

          <div className="card-status-pedido-7">
            <div className="card-documento">
              <div className="img-wrapper">
                <img className="product-4" alt="Product" src={product2} />
              </div>

              <div className="content-6">
                <div className="text-wrapper-20">Mercado</div>

                <div className="text-wrapper-13">Super Econômico</div>
              </div>
            </div>

            <div className="steps">
              <div className="frame" />

              <div className="frame" />

              <div className="frame" />

              <div className="frame" />
            </div>

            <div className="hora">
              <div className="text-wrapper-21">15:25</div>

              <div className="frame-2" />

              <div className="text-wrapper-22">Concluído</div>
            </div>
          </div>
        </div>

        <div className="content-7">
          <p className="text-wrapper-23">
            Aumente suas vendas e conquiste novos clientes com a Izy
          </p>

          <button className="button-5">
            <div className="text-wrapper-7">Quero vender pela Izy</div>
          </button>
        </div>

        <div className="content-8">
          <div className="card-status-pedido-8">
            <img
              className="notification-5"
              alt="Notification"
              src={notification3}
            />

            <div className="div-3">
              <img className="img-3" alt="Shopping ecommerce" src={basket1} />

              <div className="text-wrapper-24">Izy Mercado</div>
            </div>
          </div>

          <div className="card-status-pedido-8">
            <img
              className="notification-5"
              alt="Notification"
              src={notification4}
            />

            <div className="div-3">
              <img className="img-3" alt="Shopping ecommerce" src={basket1} />

              <div className="text-wrapper-24">Izy Hortifruti</div>
            </div>
          </div>

          <div className="card-status-pedido-8">
            <img
              className="notification-5"
              alt="Notification"
              src={notification2}
            />

            <div className="div-3">
              <img className="img-3" alt="Shopping ecommerce" src={basket1} />

              <div className="text-wrapper-24">Izy Padaria</div>
            </div>
          </div>

          <div className="card-status-pedido-8">
            <img
              className="notification-5"
              alt="Notification"
              src={notification1}
            />

            <div className="div-3">
              <img className="img-3" alt="Shopping ecommerce" src={basket1} />

              <div className="text-wrapper-24">Izy Carnes</div>
            </div>
          </div>

          <div className="card-status-pedido-8">
            <img
              className="notification-5"
              alt="Notification"
              src={notification5}
            />

            <div className="div-3">
              <img className="img-3" alt="Shopping ecommerce" src={basket1} />

              <div className="text-wrapper-24">Izy Fit</div>
            </div>
          </div>
        </div>

        <div className="content-9">
          <img
            className="img-4"
            alt="Interface essential"
            src={heartFavorite}
          />

          <p className="text-wrapper-25">O que falam da Izy?</p>
        </div>

        <div className="button-6">
          <p className="text-wrapper-7">Quero fazer minhas compras com a Izy</p>
        </div>

        <div className="depoimento">
          <div className="card-status-pedido-9">
            <div className="notification-3">
              <div className="img-2">
                <div className="overlap-group-wrapper">
                  <div className="overlap-group-4">
                    <img
                      className="rectangle"
                      alt="Rectangle"
                      src={rectangle}
                    />

                    <img className="path-17" alt="Path" src={path7} />

                    <img className="path-18" alt="Path" src={image1} />

                    <img className="path-19" alt="Path" src={path9} />

                    <div className="rectangle-2" />

                    <img className="path-20" alt="Path" src={path14} />

                    <img className="path-21" alt="Path" src={path10} />

                    <img className="path-22" alt="Path" src={path9} />
                  </div>
                </div>
              </div>
            </div>

            <div className="content-10">
              <p className="text-wrapper-26">
                Com a Izy, não preciso mais ficar horas num supermercado
                esperando numa fila, recebo meus produtos no conforto de minha
                casa!
              </p>

              <div className="content-11">
                <div className="text-wrapper-27">Lívia Maria</div>

                <div className="text-wrapper-9">Cliente Izy</div>
              </div>
            </div>
          </div>

          <div className="footer-2">
            <div className="steps-2">
              <div className="frame-3" />

              <div className="frame-4" />

              <div className="frame-4" />
            </div>

            <img className="img" alt="Actions" src={actions} />
          </div>
        </div>

        <div className="card-status-pedido-10">
          <img className="img" alt="Quer GANHAR r" src={QUERGanharR50} />

          <p className="use-o-cupom">
            <span className="text-wrapper-28">Use o cupom </span>

            <span className="text-wrapper-29">50IZY</span>
          </p>

          <p className="text-wrapper-30">*Para compras acima de R$ 500</p>
        </div>

        <div className="header">
          <div className="title-bar-buttons">
            <div className="minimize-button" />

            <div className="zoom-button" />

            <div className="close-button" />
          </div>

          <div className="element">
            <img
              className="img-3"
              alt="Show side menu"
              src={showSideMenuButton}
            />

            <img
              className="vertical-divider"
              alt="Vertical divider"
              src={verticalDivider}
            />

            <img
              className="small-arrow-down"
              alt="Small arrow down"
              src={smallArrowDown}
            />
          </div>

          <div className="details">
            <img className="img-3" alt="Arrow icon" src={arrowIcon} />

            <div className="tabs">
              <div className="active-tab">
                <div className="domain">
                  <img className="img-5" alt="Favicon" src={favicon} />

                  <div className="domain-2">izymercado.com.br</div>

                  <img className="lock" alt="Lock" src={lock} />
                </div>

                <img className="more-icon" alt="More icon" src={moreIcon} />
              </div>

              <div className="other-tabs">
                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon3}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon5}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon-2"
                    alt="Website favicon"
                    src={websiteFavicon6}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon9}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon8}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon4}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon2}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon7}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={image}
                  />
                </div>

                <div className="dark-mode-collapsed">
                  <img
                    className="website-favicon"
                    alt="Website favicon"
                    src={websiteFavicon10}
                  />
                </div>
              </div>
            </div>

            <img className="img-6" alt="Add tab button" src={addTabButton} />
          </div>
        </div>

        <div className="menu">
          <div className="logo">
            <img className="img-2" alt="Shopping ecommerce" src={basket2} />

            <div className="text-wrapper-31">izy</div>
          </div>

          <div className="menu-2">
            <div className="text-wrapper-32">Seja nosso parceiro</div>

            <div className="text-wrapper-32">Suporte</div>
          </div>

          <div className="button-7">
            <p className="text-wrapper-7">
              Quero fazer minhas compras com a Izy
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
