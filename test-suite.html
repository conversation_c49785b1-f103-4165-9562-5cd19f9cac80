<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Izy Me<PERSON>do - Test Suite</title>
  <style>
    body {
      font-family: 'Roboto', sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
    }
    
    .test-section h2 {
      color: #B771E5;
      margin-top: 0;
    }
    
    .test-result {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    
    .test-pass {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .test-fail {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .test-warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    button {
      background: linear-gradient(90deg, #B771E5 0%, #ED9292 100%);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 25px;
      cursor: pointer;
      margin: 5px;
    }
    
    button:hover {
      opacity: 0.9;
    }
    
    .metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }
    
    .metric {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      text-align: center;
    }
    
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: #B771E5;
    }
    
    .metric-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>Izy Mercado Landing Page - Test Suite</h1>
    <p>Comprehensive testing and validation for the landing page v2.0</p>
    
    <div class="test-section">
      <h2>🔍 HTML Validation</h2>
      <button onclick="runHTMLValidation()">Run HTML Validation</button>
      <div id="html-results"></div>
    </div>
    
    <div class="test-section">
      <h2>♿ Accessibility Tests</h2>
      <button onclick="runAccessibilityTests()">Run Accessibility Tests</button>
      <div id="accessibility-results"></div>
    </div>
    
    <div class="test-section">
      <h2>📱 Responsive Design Tests</h2>
      <button onclick="runResponsiveTests()">Run Responsive Tests</button>
      <div id="responsive-results"></div>
    </div>
    
    <div class="test-section">
      <h2>⚡ Performance Tests</h2>
      <button onclick="runPerformanceTests()">Run Performance Tests</button>
      <div id="performance-results"></div>
      <div class="metrics" id="performance-metrics"></div>
    </div>
    
    <div class="test-section">
      <h2>🔧 Functionality Tests</h2>
      <button onclick="runFunctionalityTests()">Run Functionality Tests</button>
      <div id="functionality-results"></div>
    </div>
    
    <div class="test-section">
      <h2>🖼️ Image Optimization Tests</h2>
      <button onclick="runImageTests()">Run Image Tests</button>
      <div id="image-results"></div>
    </div>
    
    <div class="test-section">
      <h2>📊 SEO Tests</h2>
      <button onclick="runSEOTests()">Run SEO Tests</button>
      <div id="seo-results"></div>
    </div>
    
    <div class="test-section">
      <h2>🌐 Cross-Browser Compatibility</h2>
      <button onclick="runCompatibilityTests()">Run Compatibility Tests</button>
      <div id="compatibility-results"></div>
    </div>
  </div>

  <script>
    // Test utilities
    function addResult(containerId, message, type = 'pass') {
      const container = document.getElementById(containerId);
      const result = document.createElement('div');
      result.className = `test-result test-${type}`;
      result.textContent = message;
      container.appendChild(result);
    }
    
    function clearResults(containerId) {
      document.getElementById(containerId).innerHTML = '';
    }
    
    // HTML Validation Tests
    function runHTMLValidation() {
      clearResults('html-results');
      
      // Check DOCTYPE
      if (document.doctype && document.doctype.name === 'html') {
        addResult('html-results', '✓ Valid HTML5 DOCTYPE found');
      } else {
        addResult('html-results', '✗ Missing or invalid DOCTYPE', 'fail');
      }
      
      // Check lang attribute
      if (document.documentElement.lang) {
        addResult('html-results', `✓ Language attribute set: ${document.documentElement.lang}`);
      } else {
        addResult('html-results', '✗ Missing lang attribute on html element', 'fail');
      }
      
      // Check meta charset
      const charset = document.querySelector('meta[charset]');
      if (charset) {
        addResult('html-results', '✓ Character encoding specified');
      } else {
        addResult('html-results', '✗ Missing character encoding', 'fail');
      }
      
      // Check viewport meta
      const viewport = document.querySelector('meta[name="viewport"]');
      if (viewport) {
        addResult('html-results', '✓ Viewport meta tag found');
      } else {
        addResult('html-results', '✗ Missing viewport meta tag', 'fail');
      }
      
      // Check for semantic HTML5 elements
      const semanticElements = ['header', 'main', 'section', 'article', 'footer', 'nav'];
      semanticElements.forEach(element => {
        if (document.querySelector(element)) {
          addResult('html-results', `✓ Semantic element found: <${element}>`);
        } else {
          addResult('html-results', `⚠ Semantic element missing: <${element}>`, 'warning');
        }
      });
    }
    
    // Accessibility Tests
    function runAccessibilityTests() {
      clearResults('accessibility-results');
      
      // Check for alt attributes on images
      const images = document.querySelectorAll('img');
      let imagesWithAlt = 0;
      images.forEach(img => {
        if (img.alt !== undefined) imagesWithAlt++;
      });
      
      if (imagesWithAlt === images.length) {
        addResult('accessibility-results', `✓ All ${images.length} images have alt attributes`);
      } else {
        addResult('accessibility-results', `✗ ${images.length - imagesWithAlt} images missing alt attributes`, 'fail');
      }
      
      // Check for proper heading hierarchy
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      if (headings.length > 0) {
        addResult('accessibility-results', `✓ Found ${headings.length} headings`);
        
        // Check for h1
        const h1 = document.querySelector('h1');
        if (h1) {
          addResult('accessibility-results', '✓ H1 element found');
        } else {
          addResult('accessibility-results', '✗ Missing H1 element', 'fail');
        }
      }
      
      // Check for skip links
      const skipLink = document.querySelector('.skip-link');
      if (skipLink) {
        addResult('accessibility-results', '✓ Skip link found');
      } else {
        addResult('accessibility-results', '⚠ Skip link not found', 'warning');
      }
      
      // Check for ARIA labels on buttons
      const buttons = document.querySelectorAll('button');
      let buttonsWithLabels = 0;
      buttons.forEach(btn => {
        if (btn.getAttribute('aria-label') || btn.textContent.trim()) {
          buttonsWithLabels++;
        }
      });
      
      if (buttonsWithLabels === buttons.length) {
        addResult('accessibility-results', `✓ All ${buttons.length} buttons have accessible labels`);
      } else {
        addResult('accessibility-results', `⚠ ${buttons.length - buttonsWithLabels} buttons may need better labels`, 'warning');
      }
    }
    
    // Responsive Design Tests
    function runResponsiveTests() {
      clearResults('responsive-results');
      
      // Check viewport width
      const viewportWidth = window.innerWidth;
      addResult('responsive-results', `Current viewport width: ${viewportWidth}px`);
      
      // Test different breakpoints
      const breakpoints = [320, 768, 1024, 1200];
      breakpoints.forEach(bp => {
        if (viewportWidth >= bp) {
          addResult('responsive-results', `✓ Viewport supports ${bp}px breakpoint`);
        }
      });
      
      // Check for responsive images
      const responsiveImages = document.querySelectorAll('img[srcset], picture');
      if (responsiveImages.length > 0) {
        addResult('responsive-results', `✓ Found ${responsiveImages.length} responsive images`);
      } else {
        addResult('responsive-results', '⚠ No responsive images found', 'warning');
      }
      
      // Check CSS Grid/Flexbox usage
      const gridElements = document.querySelectorAll('.grid, .features__grid, .steps__grid');
      const flexElements = document.querySelectorAll('.flex, .header__nav, .cta-section__buttons');
      
      addResult('responsive-results', `✓ Found ${gridElements.length} CSS Grid layouts`);
      addResult('responsive-results', `✓ Found ${flexElements.length} Flexbox layouts`);
    }
    
    // Performance Tests
    function runPerformanceTests() {
      clearResults('performance-results');
      clearResults('performance-metrics');
      
      if ('performance' in window) {
        const perfData = performance.getEntriesByType('navigation')[0];
        
        if (perfData) {
          const metrics = {
            'DOM Content Loaded': Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
            'Load Complete': Math.round(perfData.loadEventEnd - perfData.loadEventStart),
            'Total Load Time': Math.round(perfData.loadEventEnd - perfData.fetchStart),
            'DNS Lookup': Math.round(perfData.domainLookupEnd - perfData.domainLookupStart)
          };
          
          Object.entries(metrics).forEach(([label, value]) => {
            const metric = document.createElement('div');
            metric.className = 'metric';
            metric.innerHTML = `
              <div class="metric-value">${value}ms</div>
              <div class="metric-label">${label}</div>
            `;
            document.getElementById('performance-metrics').appendChild(metric);
          });
          
          addResult('performance-results', '✓ Performance metrics collected');
          
          // Performance recommendations
          if (metrics['Total Load Time'] < 3000) {
            addResult('performance-results', '✓ Good load time (< 3s)');
          } else {
            addResult('performance-results', '⚠ Load time could be improved', 'warning');
          }
        }
      }
      
      // Check for lazy loading
      const lazyImages = document.querySelectorAll('img[loading="lazy"]');
      if (lazyImages.length > 0) {
        addResult('performance-results', `✓ Found ${lazyImages.length} lazy-loaded images`);
      }
      
      // Check for preload hints
      const preloadLinks = document.querySelectorAll('link[rel="preload"]');
      if (preloadLinks.length > 0) {
        addResult('performance-results', `✓ Found ${preloadLinks.length} preload hints`);
      }
    }
    
    // Functionality Tests
    function runFunctionalityTests() {
      clearResults('functionality-results');
      
      // Test form validation
      const form = document.getElementById('lead-form');
      if (form) {
        addResult('functionality-results', '✓ Newsletter form found');
        
        const emailInput = form.querySelector('input[type="email"]');
        if (emailInput) {
          addResult('functionality-results', '✓ Email input found');
          
          // Test email validation
          emailInput.value = 'invalid-email';
          if (!emailInput.checkValidity()) {
            addResult('functionality-results', '✓ Email validation working');
          }
          emailInput.value = '';
        }
      }
      
      // Test button functionality
      const buttons = document.querySelectorAll('button, .btn');
      addResult('functionality-results', `✓ Found ${buttons.length} interactive buttons`);
      
      // Test smooth scrolling
      if (document.documentElement.style.scrollBehavior === 'smooth' || 
          getComputedStyle(document.documentElement).scrollBehavior === 'smooth') {
        addResult('functionality-results', '✓ Smooth scrolling enabled');
      }
    }
    
    // Image Optimization Tests
    function runImageTests() {
      clearResults('image-results');
      
      const images = document.querySelectorAll('img');
      let optimizedImages = 0;
      
      images.forEach(img => {
        // Check for width/height attributes
        if (img.width && img.height) {
          optimizedImages++;
        }
        
        // Check for loading attribute
        if (img.loading) {
          optimizedImages++;
        }
      });
      
      addResult('image-results', `✓ Found ${images.length} images`);
      addResult('image-results', `✓ ${optimizedImages} images have optimization attributes`);
      
      // Check for WebP support
      const webpImages = document.querySelectorAll('source[type="image/webp"]');
      if (webpImages.length > 0) {
        addResult('image-results', `✓ Found ${webpImages.length} WebP images`);
      } else {
        addResult('image-results', '⚠ No WebP images found', 'warning');
      }
      
      // Check for picture elements
      const pictureElements = document.querySelectorAll('picture');
      if (pictureElements.length > 0) {
        addResult('image-results', `✓ Found ${pictureElements.length} picture elements`);
      }
    }
    
    // SEO Tests
    function runSEOTests() {
      clearResults('seo-results');
      
      // Check title
      const title = document.querySelector('title');
      if (title && title.textContent.length > 0) {
        addResult('seo-results', `✓ Page title: "${title.textContent}"`);
        if (title.textContent.length > 60) {
          addResult('seo-results', '⚠ Title might be too long for search results', 'warning');
        }
      }
      
      // Check meta description
      const description = document.querySelector('meta[name="description"]');
      if (description) {
        addResult('seo-results', '✓ Meta description found');
        if (description.content.length > 160) {
          addResult('seo-results', '⚠ Meta description might be too long', 'warning');
        }
      } else {
        addResult('seo-results', '✗ Missing meta description', 'fail');
      }
      
      // Check Open Graph tags
      const ogTags = document.querySelectorAll('meta[property^="og:"]');
      if (ogTags.length > 0) {
        addResult('seo-results', `✓ Found ${ogTags.length} Open Graph tags`);
      }
      
      // Check structured data
      const structuredData = document.querySelectorAll('script[type="application/ld+json"]');
      if (structuredData.length > 0) {
        addResult('seo-results', `✓ Found ${structuredData.length} structured data blocks`);
      } else {
        addResult('seo-results', '⚠ No structured data found', 'warning');
      }
    }
    
    // Cross-Browser Compatibility Tests
    function runCompatibilityTests() {
      clearResults('compatibility-results');
      
      // Browser detection
      const userAgent = navigator.userAgent;
      addResult('compatibility-results', `Browser: ${userAgent}`);
      
      // Feature detection
      const features = {
        'CSS Grid': 'CSS' in window && CSS.supports('display', 'grid'),
        'CSS Flexbox': 'CSS' in window && CSS.supports('display', 'flex'),
        'Service Workers': 'serviceWorker' in navigator,
        'Intersection Observer': 'IntersectionObserver' in window,
        'Fetch API': 'fetch' in window,
        'ES6 Classes': typeof class {} === 'function',
        'CSS Custom Properties': 'CSS' in window && CSS.supports('color', 'var(--test)')
      };
      
      Object.entries(features).forEach(([feature, supported]) => {
        if (supported) {
          addResult('compatibility-results', `✓ ${feature} supported`);
        } else {
          addResult('compatibility-results', `✗ ${feature} not supported`, 'fail');
        }
      });
    }
    
    // Auto-run basic tests on load
    window.addEventListener('load', () => {
      console.log('Test suite loaded. Click buttons to run specific tests.');
    });
  </script>
</body>
</html>
